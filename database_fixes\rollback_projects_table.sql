-- VanHub数据库回滚脚本
-- 执行日期：2025-01-27
-- 目的：如果需要，可以撤销对projects表的修改

-- ⚠️ 警告：此脚本会删除添加的列及其数据，请谨慎使用！

-- ========================================
-- 回滚操作（仅在必要时执行）
-- ========================================

DO $$ 
BEGIN
    RAISE NOTICE '开始回滚projects表修改...';
    
    -- 1. 删除tags列（如果存在）
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'projects' 
        AND column_name = 'tags' 
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.projects DROP COLUMN tags;
        RAISE NOTICE '✅ tags列已删除';
    ELSE
        RAISE NOTICE '⚠️ tags列不存在，跳过删除';
    END IF;
    
    -- 2. 删除vehicle_type列（如果存在）
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'projects' 
        AND column_name = 'vehicle_type' 
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.projects DROP COLUMN vehicle_type;
        RAISE NOTICE '✅ vehicle_type列已删除';
    ELSE
        RAISE NOTICE '⚠️ vehicle_type列不存在，跳过删除';
    END IF;
    
    -- 3. 删除vehicle_brand列（如果存在）
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'projects' 
        AND column_name = 'vehicle_brand' 
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.projects DROP COLUMN vehicle_brand;
        RAISE NOTICE '✅ vehicle_brand列已删除';
    ELSE
        RAISE NOTICE '⚠️ vehicle_brand列不存在，跳过删除';
    END IF;
    
    RAISE NOTICE '回滚操作完成！';
END $$;

-- 验证回滚结果
SELECT 
    '=== 回滚后的表结构 ===' as info,
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'projects' 
AND table_schema = 'public'
ORDER BY ordinal_position;
