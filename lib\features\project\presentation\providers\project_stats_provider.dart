import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../domain/entities/project_stats.dart';
import '../../domain/services/project_stats_service.dart';

part 'project_stats_provider.g.dart';

/// 项目统计服务Provider - 通过依赖注入获取，遵循Clean Architecture
@riverpod
ProjectStatsService projectStatsService(Ref ref) {
  return ref.watch(projectStatsServiceProvider);
}

/// 项目统计数据Provider
@riverpod
Future<ProjectStats> projectStats(Ref ref, String projectId) async {
  final service = ref.watch(projectStatsServiceProvider);
  final result = await service.getProjectStats(projectId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (stats) => stats,
  );
}

/// 项目进度Provider
@riverpod
Future<double> projectProgress(Ref ref, String projectId) async {
  final service = ref.watch(projectStatsServiceProvider);
  final result = await service.calculateProjectProgress(projectId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (progress) => progress,
  );
}

/// BOM状态统计Provider
@riverpod
Future<Map<String, int>> bomStatusCounts(Ref ref, String projectId) async {
  final service = ref.watch(projectStatsServiceProvider);
  final result = await service.getBomItemStatusCounts(projectId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (counts) => counts,
  );
}

/// 预算分析Provider
@riverpod
Future<BudgetAnalysis> budgetAnalysis(Ref ref, String projectId) async {
  final service = ref.watch(projectStatsServiceProvider);
  final result = await service.calculateBudgetAnalysis(projectId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (analysis) => analysis,
  );
}

/// 分类成本分布Provider
@riverpod
Future<Map<String, double>> categoryCostDistribution(
  Ref ref, 
  String projectId,
) async {
  final service = ref.watch(projectStatsServiceProvider);
  final result = await service.getCategoryCostDistribution(projectId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (distribution) => distribution,
  );
}

/// 预计完成时间Provider
@riverpod
Future<DateTime?> estimatedCompletionDate(
  Ref ref,
  String projectId,
) async {
  final service = ref.watch(projectStatsServiceProvider);
  final result = await service.estimateCompletionDate(projectId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (date) => date,
  );
}

/// 项目效率指标Provider
@riverpod
Future<ProjectEfficiencyMetrics> projectEfficiencyMetrics(
  Ref ref,
  String projectId,
) async {
  final service = ref.watch(projectStatsServiceProvider);
  final result = await service.getEfficiencyMetrics(projectId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (metrics) => metrics,
  );
}

/// 项目比较Provider
@riverpod
Future<ProjectComparison> projectComparison(
  Ref ref,
  String projectId,
) async {
  final service = ref.watch(projectStatsServiceProvider);
  final result = await service.compareWithAverage(projectId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (comparison) => comparison,
  );
}

/// 项目风险评估Provider
@riverpod
Future<ProjectRiskAssessment> projectRiskAssessment(
  Ref ref,
  String projectId,
) async {
  final service = ref.watch(projectStatsServiceProvider);
  final result = await service.assessProjectRisk(projectId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (assessment) => assessment,
  );
}

/// 项目报告Provider
@riverpod
Future<ProjectReport> projectReport(
  Ref ref,
  String projectId,
  ProjectReportType reportType,
) async {
  final service = ref.watch(projectStatsServiceProvider);
  final result = await service.generateProjectReport(projectId, reportType);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (report) => report,
  );
}

/// 项目统计摘要Provider
/// 提供项目的关键统计指标摘要
@riverpod
Future<ProjectStatsSummary> projectStatsSummary(
  Ref ref,
  String projectId,
) async {
  // 直接使用service获取统计数据，避免循环依赖
  final service = ref.watch(projectStatsServiceProvider);
  final result = await service.getProjectStats(projectId);

  final stats = result.fold(
    (failure) => throw Exception(failure.message),
    (stats) => stats,
  );

  return ProjectStatsSummary(
    projectId: projectId,
    progressPercentage: stats.progressPercentage,
    totalBudget: stats.totalBudget,
    actualCost: stats.actualCost,
    totalBomItems: stats.totalBomItems,
    completedBomItems: stats.completedBomItems,
    budgetHealthStatus: stats.budgetHealthStatus,
    progressStatus: stats.progressStatus,
    isOverBudget: stats.isOverBudget,
    topCostCategory: stats.topCostCategory,
    estimatedCompletionDate: stats.estimatedCompletionDate,
  );
}

/// 项目统计摘要实体
/// 用于在UI中显示关键统计信息
class ProjectStatsSummary {
  final String projectId;
  final double progressPercentage;
  final double totalBudget;
  final double actualCost;
  final int totalBomItems;
  final int completedBomItems;
  final BudgetHealthStatus budgetHealthStatus;
  final ProjectProgressStatus progressStatus;
  final bool isOverBudget;
  final String? topCostCategory;
  final DateTime? estimatedCompletionDate;
  
  const ProjectStatsSummary({
    required this.projectId,
    required this.progressPercentage,
    required this.totalBudget,
    required this.actualCost,
    required this.totalBomItems,
    required this.completedBomItems,
    required this.budgetHealthStatus,
    required this.progressStatus,
    required this.isOverBudget,
    this.topCostCategory,
    this.estimatedCompletionDate,
  });
  
  /// 格式化进度百分比
  String get formattedProgress => '${progressPercentage.toStringAsFixed(1)}%';
  
  /// 格式化总预算
  String get formattedTotalBudget => '¥${totalBudget.toStringAsFixed(0)}';
  
  /// 格式化实际成本
  String get formattedActualCost => '¥${actualCost.toStringAsFixed(0)}';
  
  /// 格式化剩余预算
  String get formattedRemainingBudget {
    final remaining = totalBudget - actualCost;
    return '¥${remaining.toStringAsFixed(0)}';
  }
  
  /// 完成率描述
  String get completionDescription {
    if (totalBomItems == 0) return '暂无项目';
    return '$completedBomItems/$totalBomItems 项已完成';
  }
  
  /// 预算状态描述
  String get budgetStatusDescription {
    if (isOverBudget) {
      final overrun = actualCost - totalBudget;
      return '超支 ¥${overrun.toStringAsFixed(0)}';
    } else {
      final remaining = totalBudget - actualCost;
      return '剩余 ¥${remaining.toStringAsFixed(0)}';
    }
  }
  
  /// 预计完成时间描述
  String get estimatedCompletionDescription {
    if (estimatedCompletionDate == null) return '未设定';
    
    final now = DateTime.now();
    final difference = estimatedCompletionDate!.difference(now).inDays;
    
    if (difference < 0) {
      return '已逾期 ${-difference} 天';
    } else if (difference == 0) {
      return '今天完成';
    } else if (difference == 1) {
      return '明天完成';
    } else if (difference <= 7) {
      return '$difference 天后完成';
    } else if (difference <= 30) {
      final weeks = (difference / 7).ceil();
      return '$weeks 周后完成';
    } else {
      final months = (difference / 30).ceil();
      return '$months 个月后完成';
    }
  }
}

/// 项目统计刷新Provider
/// 用于手动刷新项目统计数据
@riverpod
class ProjectStatsRefresher extends _$ProjectStatsRefresher {
  @override
  bool build() => false;
  
  /// 刷新指定项目的统计数据
  Future<void> refreshProjectStats(String projectId) async {
    state = true;

    try {
      // 刷新所有相关的Provider
      ref.invalidate(projectStatsProvider(projectId));
      ref.invalidate(projectProgressProvider(projectId));
      ref.invalidate(bomStatusCountsProvider(projectId));
      ref.invalidate(budgetAnalysisProvider(projectId));
      ref.invalidate(categoryCostDistributionProvider(projectId));
      ref.invalidate(estimatedCompletionDateProvider(projectId));
      ref.invalidate(projectEfficiencyMetricsProvider(projectId));
      ref.invalidate(projectComparisonProvider(projectId));
      ref.invalidate(projectRiskAssessmentProvider(projectId));
      ref.invalidate(projectStatsSummaryProvider(projectId));

      // 等待数据重新加载
      await Future.delayed(const Duration(milliseconds: 500));
    } finally {
      state = false;
    }
  }
  
  /// 刷新所有项目的统计数据
  Future<void> refreshAllProjectStats() async {
    state = true;
    
    try {
      // 刷新项目统计服务
      ref.invalidate(projectStatsServiceProvider);
      
      // 等待数据重新加载
      await Future.delayed(const Duration(milliseconds: 500));
    } finally {
      state = false;
    }
  }
}

/// 项目统计缓存Provider
/// 用于缓存项目统计数据，提高性能
@riverpod
class ProjectStatsCache extends _$ProjectStatsCache {
  @override
  Map<String, ProjectStats> build() => {};
  
  /// 获取缓存的项目统计
  ProjectStats? getCachedStats(String projectId) {
    return state[projectId];
  }
  
  /// 缓存项目统计
  void cacheStats(String projectId, ProjectStats stats) {
    state = {...state, projectId: stats};
  }
  
  /// 清除指定项目的缓存
  void clearCache(String projectId) {
    final newState = Map<String, ProjectStats>.from(state);
    newState.remove(projectId);
    state = newState;
  }
  
  /// 清除所有缓存
  void clearAllCache() {
    state = {};
  }
  
  /// 检查缓存是否过期（5分钟）
  bool isCacheExpired(String projectId) {
    final cachedStats = state[projectId];
    if (cachedStats == null) return true;
    
    final now = DateTime.now();
    final cacheAge = now.difference(cachedStats.lastUpdated);
    return cacheAge.inMinutes > 5;
  }
}
