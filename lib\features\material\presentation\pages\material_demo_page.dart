import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/design_system/foundation/colors/colors.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';
import '../../domain/entities/material.dart' as domain;
import '../widgets/material_card_v2.dart';
import '../widgets/material_search_bar.dart';
import '../widgets/material_filter_panel.dart';
import 'material_library_page_v3.dart';

/// 物料界面演示页面
/// 用于展示重构后的物料显示界面效果
class MaterialDemoPage extends ConsumerStatefulWidget {
  const MaterialDemoPage({super.key});

  @override
  ConsumerState<MaterialDemoPage> createState() => _MaterialDemoPageState();
}

class _MaterialDemoPageState extends ConsumerState<MaterialDemoPage> {
  String _searchQuery = '';
  String _selectedCategory = '全部';
  bool _showFilters = false;
  MaterialViewType _viewType = MaterialViewType.grid;

  // 模拟数据
  final List<domain.Material> _mockMaterials = [
    domain.Material(
      id: '1',
      userId: 'user1',
      name: '德力西空气开关',
      description: '高品质空气开关，安全可靠',
      category: '电路系统',
      price: 89.00,
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      brand: '德力西',
      specifications: 'DZ47-63 C32 2P',
      purchaseDate: DateTime.now().subtract(const Duration(days: 5)),
      tags: ['高性价比', '品质优选'],
    ),
    domain.Material(
      id: '2',
      userId: 'user1',
      name: '潜水泵',
      description: '静音潜水泵，适用于房车水路系统',
      category: '水路系统',
      price: 268.00,
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      brand: '格兰富',
      specifications: '12V 15L/min',
      purchaseDate: DateTime.now().subtract(const Duration(days: 10)),
      tags: ['静音', '节能'],
    ),
    domain.Material(
      id: '3',
      userId: 'user1',
      name: '房车专用床垫',
      description: '记忆棉床垫，舒适透气',
      category: '床铺系统',
      price: 1299.00,
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
      updatedAt: DateTime.now().subtract(const Duration(days: 3)),
      brand: '慕思',
      specifications: '1800x2000x150mm',
      purchaseDate: DateTime.now().subtract(const Duration(days: 15)),
      tags: ['舒适', '透气'],
    ),
    domain.Material(
      id: '4',
      userId: 'user1',
      name: '储物箱',
      description: '多功能储物箱，防水防潮',
      category: '储物系统',
      price: 158.00,
      createdAt: DateTime.now().subtract(const Duration(days: 4)),
      updatedAt: DateTime.now().subtract(const Duration(days: 4)),
      brand: '乐扣乐扣',
      specifications: '600x400x300mm',
      purchaseDate: DateTime.now().subtract(const Duration(days: 20)),
      tags: ['防水', '耐用'],
    ),
    domain.Material(
      id: '5',
      userId: 'user1',
      name: '车载冰箱',
      description: '压缩机制冷，节能静音',
      category: '厨房系统',
      price: 2899.00,
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      updatedAt: DateTime.now().subtract(const Duration(days: 5)),
      brand: '英得尔',
      specifications: '40L 12V/24V',
      purchaseDate: DateTime.now().subtract(const Duration(days: 25)),
      tags: ['节能', '静音', '大容量'],
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: VanHubColors.surface,
      appBar: AppBar(
        title: const Text('物料界面演示'),
        backgroundColor: VanHubColors.primary,
        foregroundColor: VanHubColors.onPrimary,
        actions: [
          IconButton(
            icon: Icon(_getViewTypeIcon()),
            onPressed: _toggleViewType,
            tooltip: '切换视图',
          ),
        ],
      ),
      body: Column(
        children: [
          // 搜索栏
          Padding(
            padding: EdgeInsets.all(VanHubSpacing.lg),
            child: MaterialSearchBar(
              searchQuery: _searchQuery,
              onSearchChanged: (query) {
                setState(() {
                  _searchQuery = query;
                });
              },
              onFilterToggle: () {
                setState(() {
                  _showFilters = !_showFilters;
                });
              },
              showFilters: _showFilters,
            ),
          ),

          // 筛选面板
          if (_showFilters)
            MaterialFilterPanel(
              selectedCategory: _selectedCategory,
              onCategoryChanged: (category) {
                setState(() {
                  _selectedCategory = category;
                });
              },
              onReset: () {
                setState(() {
                  _searchQuery = '';
                  _selectedCategory = '全部';
                });
              },
            ),

          // 物料列表
          Expanded(
            child: _buildMaterialList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('添加新物料')),
          );
        },
        backgroundColor: VanHubColors.primary,
        foregroundColor: VanHubColors.onPrimary,
        icon: const Icon(Icons.add),
        label: const Text('添加物料'),
      ),
    );
  }

  Widget _buildMaterialList() {
    final filteredMaterials = _filterMaterials();

    if (filteredMaterials.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: VanHubColors.onSurface.withOpacity(0.3),
            ),
            const SizedBox(height: VanHubSpacing.lg),
            Text(
              '未找到匹配的物料',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: VanHubColors.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: VanHubSpacing.sm),
            Text(
              '尝试调整搜索条件或分类筛选',
              style: TextStyle(
                fontSize: 14,
                color: VanHubColors.onSurface.withOpacity(0.5),
              ),
            ),
          ],
        ),
      );
    }

    switch (_viewType) {
      case MaterialViewType.grid:
        return _buildGridView(filteredMaterials);
      case MaterialViewType.list:
        return _buildListView(filteredMaterials);
      case MaterialViewType.compact:
        return _buildCompactView(filteredMaterials);
    }
  }

  Widget _buildGridView(List<domain.Material> materials) {
    return Padding(
      padding: EdgeInsets.all(VanHubSpacing.lg),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.75,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: materials.length,
        itemBuilder: (context, index) {
          return MaterialCardV2(
            material: materials[index],
            viewType: MaterialCardViewType.grid,
            onTap: () => _showMaterialDetails(materials[index]),
            onEdit: () => _editMaterial(materials[index]),
            onDelete: () => _deleteMaterial(materials[index]),
            onAddToBom: () => _addToBom(materials[index]),
          );
        },
      ),
    );
  }

  Widget _buildListView(List<domain.Material> materials) {
    return ListView.builder(
      padding: EdgeInsets.all(VanHubSpacing.lg),
      itemCount: materials.length,
      itemBuilder: (context, index) {
        return Container(
          margin: EdgeInsets.only(bottom: VanHubSpacing.md),
          child: MaterialCardV2(
            material: materials[index],
            viewType: MaterialCardViewType.list,
            onTap: () => _showMaterialDetails(materials[index]),
            onEdit: () => _editMaterial(materials[index]),
            onDelete: () => _deleteMaterial(materials[index]),
            onAddToBom: () => _addToBom(materials[index]),
          ),
        );
      },
    );
  }

  Widget _buildCompactView(List<domain.Material> materials) {
    return ListView.builder(
      padding: EdgeInsets.all(VanHubSpacing.lg),
      itemCount: materials.length,
      itemBuilder: (context, index) {
        return Container(
          margin: EdgeInsets.only(bottom: VanHubSpacing.sm),
          child: MaterialCardV2(
            material: materials[index],
            viewType: MaterialCardViewType.compact,
            onTap: () => _showMaterialDetails(materials[index]),
            onEdit: () => _editMaterial(materials[index]),
            onDelete: () => _deleteMaterial(materials[index]),
            onAddToBom: () => _addToBom(materials[index]),
          ),
        );
      },
    );
  }

  List<domain.Material> _filterMaterials() {
    return _mockMaterials.where((material) {
      final matchesSearch = _searchQuery.trim().isEmpty ||
          material.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (material.brand?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) ||
          (material.specifications?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);

      final matchesCategory = _selectedCategory == '全部' ||
          material.category == _selectedCategory;

      return matchesSearch && matchesCategory;
    }).toList();
  }

  void _toggleViewType() {
    setState(() {
      switch (_viewType) {
        case MaterialViewType.grid:
          _viewType = MaterialViewType.list;
          break;
        case MaterialViewType.list:
          _viewType = MaterialViewType.compact;
          break;
        case MaterialViewType.compact:
          _viewType = MaterialViewType.grid;
          break;
      }
    });
  }

  IconData _getViewTypeIcon() {
    switch (_viewType) {
      case MaterialViewType.grid:
        return Icons.view_list_outlined;
      case MaterialViewType.list:
        return Icons.view_compact_outlined;
      case MaterialViewType.compact:
        return Icons.grid_view_outlined;
    }
  }

  void _showMaterialDetails(domain.Material material) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('查看物料详情: ${material.name}')),
    );
  }

  void _editMaterial(domain.Material material) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('编辑物料: ${material.name}')),
    );
  }

  void _deleteMaterial(domain.Material material) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('删除物料: ${material.name}')),
    );
  }

  void _addToBom(domain.Material material) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('添加到BOM: ${material.name}')),
    );
  }
}
