-- VanHub数据库诊断脚本
-- 目的：全面检查数据库状态和权限问题

-- ========================================
-- 第一部分：基础数据检查
-- ========================================

-- 1. 检查projects表是否存在
SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_name = 'projects' AND table_schema = 'public';

-- 2. 检查projects表结构
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'projects' AND table_schema = 'public'
ORDER BY ordinal_position;

-- 3. 统计projects表中的数据
SELECT 
    COUNT(*) as total_projects,
    COUNT(CASE WHEN is_public = true THEN 1 END) as public_projects,
    COUNT(CASE WHEN is_public = false THEN 1 END) as private_projects,
    COUNT(CASE WHEN is_public IS NULL THEN 1 END) as null_public_projects
FROM public.projects;

-- 4. 查看最近创建的项目
SELECT 
    id,
    title,
    is_public,
    user_id,
    created_at
FROM public.projects 
ORDER BY created_at DESC 
LIMIT 5;

-- ========================================
-- 第二部分：RLS策略检查
-- ========================================

-- 5. 检查RLS是否启用
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE tablename = 'projects' AND schemaname = 'public';

-- 6. 查看当前所有RLS策略
SELECT 
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'projects' AND schemaname = 'public'
ORDER BY policyname;

-- ========================================
-- 第三部分：权限测试
-- ========================================

-- 7. 测试匿名用户查询（模拟游客模式）
-- 这个查询应该返回所有公开项目
SELECT 
    'Anonymous user test' as test_type,
    COUNT(*) as accessible_projects
FROM public.projects 
WHERE is_public = true;

-- 8. 检查是否有权限问题
-- 尝试直接查询，看是否有错误
DO $$ 
DECLARE
    project_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO project_count FROM public.projects WHERE is_public = true;
    RAISE NOTICE '可访问的公开项目数量: %', project_count;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '查询公开项目时出错: %', SQLERRM;
END $$;

-- ========================================
-- 第四部分：Supabase特定检查
-- ========================================

-- 9. 检查auth.users表（如果可访问）
DO $$ 
BEGIN
    IF EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_name = 'users' 
        AND table_schema = 'auth'
    ) THEN
        RAISE NOTICE 'auth.users表存在';
    ELSE
        RAISE NOTICE 'auth.users表不存在或无权访问';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '检查auth.users表时出错: %', SQLERRM;
END $$;

-- 10. 检查当前用户角色
SELECT 
    current_user as current_database_user,
    session_user as session_user;

-- ========================================
-- 第五部分：数据完整性检查
-- ========================================

-- 11. 检查is_public字段的数据分布
SELECT 
    is_public,
    COUNT(*) as count
FROM public.projects 
GROUP BY is_public;

-- 12. 检查user_id字段
SELECT
    CASE
        WHEN user_id IS NULL THEN 'NULL'
        ELSE 'HAS_VALUE'
    END as user_id_status,
    COUNT(*) as count
FROM public.projects
GROUP BY
    CASE
        WHEN user_id IS NULL THEN 'NULL'
        ELSE 'HAS_VALUE'
    END;

-- ========================================
-- 第六部分：最终诊断结果
-- ========================================

-- 13. 综合诊断报告
SELECT 
    '=== 诊断完成 ===' as status,
    (SELECT COUNT(*) FROM public.projects) as total_projects,
    (SELECT COUNT(*) FROM public.projects WHERE is_public = true) as public_projects,
    (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'projects') as rls_policies;

-- 14. 建议的下一步操作
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM public.projects) = 0 THEN 
            '❌ 数据库中没有项目数据，需要检查数据插入是否成功'
        WHEN (SELECT COUNT(*) FROM public.projects WHERE is_public = true) = 0 THEN 
            '❌ 没有公开项目，需要检查is_public字段设置'
        WHEN (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'projects') = 0 THEN 
            '❌ 没有RLS策略，需要重新创建策略'
        ELSE 
            '✅ 数据和策略都存在，问题可能在应用层'
    END as diagnosis;
