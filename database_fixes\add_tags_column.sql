-- VanHub数据库完整修复脚本
-- 执行日期：2025-01-27
-- 目的：修复项目创建功能中的缺失字段问题
-- 基于实际数据库结构分析结果

-- ========================================
-- 第一部分：检查当前projects表结构
-- ========================================

SELECT
    '=== 当前projects表结构 ===' as info,
    column_name,
    data_type,
    is_nullable,
    column_default,
    ordinal_position
FROM information_schema.columns
WHERE table_name = 'projects'
AND table_schema = 'public'
ORDER BY ordinal_position;

-- ========================================
-- 第二部分：添加缺失的字段
-- ========================================

DO $$
BEGIN
    RAISE NOTICE '开始修复projects表缺失字段...';

    -- 1. 添加tags列（TEXT[]类型）
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'projects'
        AND column_name = 'tags'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.projects
        ADD COLUMN tags TEXT[] DEFAULT '{}';
        RAISE NOTICE '✅ tags列已成功添加 (类型: TEXT[])';
    ELSE
        RAISE NOTICE '⚠️ tags列已存在，跳过添加';
    END IF;

    -- 2. 添加vehicle_type列（TEXT类型）
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'projects'
        AND column_name = 'vehicle_type'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.projects
        ADD COLUMN vehicle_type TEXT DEFAULT '自行式房车';
        RAISE NOTICE '✅ vehicle_type列已成功添加 (类型: TEXT)';
    ELSE
        RAISE NOTICE '⚠️ vehicle_type列已存在，跳过添加';
    END IF;

    -- 3. 添加vehicle_brand列（TEXT类型）
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'projects'
        AND column_name = 'vehicle_brand'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.projects
        ADD COLUMN vehicle_brand TEXT;
        RAISE NOTICE '✅ vehicle_brand列已成功添加 (类型: TEXT)';
    ELSE
        RAISE NOTICE '⚠️ vehicle_brand列已存在，跳过添加';
    END IF;

    RAISE NOTICE '数据库字段修复完成！';
END $$;

-- ========================================
-- 第三部分：验证修复结果
-- ========================================

-- 验证新添加的字段
SELECT
    '=== 新添加的字段验证 ===' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'projects'
AND table_schema = 'public'
AND column_name IN ('tags', 'vehicle_type', 'vehicle_brand')
ORDER BY column_name;

-- ========================================
-- 第四部分：更新现有数据
-- ========================================

-- 为现有项目设置默认值
UPDATE public.projects
SET
    tags = COALESCE(tags, '{}'),
    vehicle_type = COALESCE(vehicle_type, '自行式房车')
WHERE tags IS NULL OR vehicle_type IS NULL;

-- ========================================
-- 第五部分：最终验证
-- ========================================

-- 统计修复结果
SELECT
    '=== 修复统计结果 ===' as info,
    COUNT(*) as total_projects,
    COUNT(CASE WHEN tags IS NOT NULL THEN 1 END) as projects_with_tags,
    COUNT(CASE WHEN vehicle_type IS NOT NULL THEN 1 END) as projects_with_vehicle_type,
    COUNT(CASE WHEN vehicle_brand IS NOT NULL THEN 1 END) as projects_with_vehicle_brand
FROM public.projects;

-- 显示修复后的完整表结构
SELECT
    '=== 修复后的完整表结构 ===' as info,
    column_name,
    data_type,
    is_nullable,
    column_default,
    ordinal_position
FROM information_schema.columns
WHERE table_name = 'projects'
AND table_schema = 'public'
ORDER BY ordinal_position;
