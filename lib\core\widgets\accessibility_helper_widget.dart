import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// 辅助功能帮助组件
/// 在Flutter Web中提供用户友好的辅助功能启用提示
class AccessibilityHelperWidget extends StatefulWidget {
  final Widget child;
  
  const AccessibilityHelperWidget({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<AccessibilityHelperWidget> createState() => _AccessibilityHelperWidgetState();
}

class _AccessibilityHelperWidgetState extends State<AccessibilityHelperWidget> {
  bool _showAccessibilityTip = false;
  
  @override
  void initState() {
    super.initState();
    
    // 只在Web平台显示提示
    if (kIsWeb) {
      // 延迟显示提示，给自动启用脚本一些时间
      Future.delayed(const Duration(seconds: 5), () {
        if (mounted) {
          setState(() {
            _showAccessibilityTip = true;
          });
          
          // 10秒后自动隐藏提示
          Future.delayed(const Duration(seconds: 10), () {
            if (mounted) {
              setState(() {
                _showAccessibilityTip = false;
              });
            }
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        
        // 辅助功能提示
        if (_showAccessibilityTip && kIsWeb)
          Positioned(
            top: 20,
            left: 20,
            right: 20,
            child: Material(
              elevation: 8,
              borderRadius: BorderRadius.circular(12),
              color: Colors.blue.shade50,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.accessibility_new,
                      color: Colors.blue.shade700,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '页面显示异常？',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade800,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '如果页面内容无法正常显示，请点击页面上的"Enable accessibility"按钮',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.blue.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        setState(() {
                          _showAccessibilityTip = false;
                        });
                      },
                      icon: Icon(
                        Icons.close,
                        color: Colors.blue.shade600,
                        size: 20,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }
}
