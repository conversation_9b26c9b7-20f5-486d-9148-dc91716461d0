<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="vanhub">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>vanhub</title>
  <link rel="manifest" href="manifest.json">
</head>
<body>
  <script src="flutter_bootstrap.js" async></script>

  <!-- 自动启用Flutter Web辅助功能 -->
  <script>
    // 等待Flutter应用完全加载后自动启用辅助功能
    function enableAccessibility() {
      const accessibilityButton = document.querySelector('flt-semantics-placeholder[role="button"][aria-label*="accessibility"]');
      if (accessibilityButton) {
        console.log('🔧 自动启用Flutter Web辅助功能');
        accessibilityButton.click();
        return true;
      }
      return false;
    }

    // 多次尝试启用辅助功能
    let attempts = 0;
    const maxAttempts = 10;

    function tryEnableAccessibility() {
      if (attempts >= maxAttempts) {
        console.log('⚠️ 无法自动启用辅助功能，请手动点击"Enable accessibility"按钮');
        return;
      }

      if (!enableAccessibility()) {
        attempts++;
        setTimeout(tryEnableAccessibility, 1000); // 每秒尝试一次
      }
    }

    // 页面加载完成后开始尝试
    window.addEventListener('load', function() {
      setTimeout(tryEnableAccessibility, 2000); // 等待2秒让Flutter完全加载
    });
  </script>
</body>
</html>
