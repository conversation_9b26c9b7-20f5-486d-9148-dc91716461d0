import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:fpdart/fpdart.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/errors/failures.dart';
import '../entities/create_project_request.dart';
import '../entities/project.dart';
import '../entities/fork_request.dart';
import '../usecases/create_project_usecase.dart';
import '../usecases/fork_project_usecase.dart';
import '../../presentation/providers/project_provider.dart';
import '../../../../core/providers/auth_state_provider.dart';

part 'project_service.g.dart';

/// 项目业务逻辑服务 - 严格遵循Clean Architecture原则
/// 
/// 职责：
/// - 纯业务逻辑处理
/// - 不涉及任何UI状态管理
/// - 返回Either类型供上层处理
/// - 确保操作的唯一性和安全性
@riverpod
class ProjectService extends _$ProjectService {
  // 防重复调用的操作锁
  final Map<String, Completer<Either<Failure, Project>>> _activeOperations = {};

  @override
  void build() {
    // 初始化服务，无状态
  }

  /// 创建项目 - 纯业务逻辑，无UI状态管理
  /// 
  /// 特性：
  /// - 防重复调用保护
  /// - 纯Either返回，不设置任何UI状态
  /// - 操作完成后自动清理资源
  Future<Either<Failure, Project>> createProject(CreateProjectRequest request) async {
    final operationKey = 'create_${request.title}_${DateTime.now().millisecondsSinceEpoch}';
    
    // 防重复调用检查
    if (_activeOperations.containsKey(operationKey)) {
      return Left(ValidationFailure(message: '项目创建操作正在进行中'));
    }

    // 创建操作Completer
    final completer = Completer<Either<Failure, Project>>();
    _activeOperations[operationKey] = completer;

    try {
      // 调用Domain层UseCase - 严格遵循Clean Architecture
      final result = await ref.read(createProjectUseCaseProvider).call(request);
      
      // 成功时异步刷新相关数据，不影响主流程
      if (result.isRight()) {
        _scheduleDataRefresh();
      }
      
      // 完成操作
      if (!completer.isCompleted) {
        completer.complete(result);
      }
      
      return result;
    } catch (e, stackTrace) {
      final failure = ServerFailure(message: '创建项目失败: $e');
      final errorResult = Left<Failure, Project>(failure);
      
      // 完成操作
      if (!completer.isCompleted) {
        completer.complete(errorResult);
      }
      
      return errorResult;
    } finally {
      // 清理操作锁
      _activeOperations.remove(operationKey);
    }
  }

  /// 复刻项目 - 纯业务逻辑
  Future<Either<Failure, Project>> forkProject({
    required String sourceProjectId,
    required String newProjectTitle,
    String? description,
    bool copyBomItems = true,
    bool copySystems = true,
    bool copyImages = false,
  }) async {
    final operationKey = 'fork_${sourceProjectId}_${DateTime.now().millisecondsSinceEpoch}';
    
    // 防重复调用检查
    if (_activeOperations.containsKey(operationKey)) {
      return Left(ValidationFailure(message: '项目复刻操作正在进行中'));
    }

    // 创建操作Completer
    final completer = Completer<Either<Failure, Project>>();
    _activeOperations[operationKey] = completer;

    try {
      final forkRequest = ForkRequest(
        sourceProjectId: sourceProjectId,
        newProjectTitle: newProjectTitle,
        description: description,
        copyBomItems: copyBomItems,
        copySystems: copySystems,
        copyImages: copyImages,
      );

      final params = ForkProjectParams(
        sourceProjectId: sourceProjectId,
        forkRequest: forkRequest,
      );

      final result = await ref.read(forkProjectUseCaseProvider).call(params);
      
      // 成功时异步刷新相关数据
      if (result.isRight()) {
        _scheduleDataRefresh();
      }
      
      // 完成操作
      if (!completer.isCompleted) {
        completer.complete(result);
      }
      
      return result;
    } catch (e, stackTrace) {
      final failure = ServerFailure(message: '复刻项目失败: $e');
      final errorResult = Left<Failure, Project>(failure);
      
      // 完成操作
      if (!completer.isCompleted) {
        completer.complete(errorResult);
      }
      
      return errorResult;
    } finally {
      // 清理操作锁
      _activeOperations.remove(operationKey);
    }
  }

  /// 安全地调度数据刷新 - 不影响主业务流程
  void _scheduleDataRefresh() {
    scheduleMicrotask(() {
      try {
        if (kDebugMode) {
          print('🔄 ProjectService: 开始刷新项目列表Provider');
        }

        // 刷新公开项目列表Provider - 使用默认参数确保一致性
        ref.invalidate(publicProjectsProvider());
        if (kDebugMode) {
          print('🔄 ProjectService: publicProjectsProvider()已刷新');
        }

        // 刷新当前用户的项目列表
        final currentUserId = ref.read(currentUserIdProvider);
        if (currentUserId != null) {
          ref.invalidate(userProjectsProvider(currentUserId));
          if (kDebugMode) {
            print('🔄 ProjectService: userProjectsProvider已刷新 (用户ID: $currentUserId)');
          }
        }

        if (kDebugMode) {
          print('🔄 ProjectService: 所有Provider刷新完成');
        }
      } catch (e) {
        // 静默处理刷新错误，不影响业务逻辑
        if (kDebugMode) {
          print('❌ ProjectService: Provider刷新失败: $e');
        }
      }
    });
  }

  /// 清理所有活跃操作 - 用于测试或重置
  void clearActiveOperations() {
    _activeOperations.clear();
  }

  /// 获取当前活跃操作数量 - 用于调试
  int get activeOperationsCount => _activeOperations.length;
}

// 临时导入 - 实际应该从正确的provider文件导入
// TODO: 替换为实际的provider导入
