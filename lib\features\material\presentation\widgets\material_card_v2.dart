import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../../core/design_system/foundation/colors/colors.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';
import '../../../../core/design_system/utils/responsive_utils.dart';
import '../../domain/entities/material.dart' as domain;
import '../pages/material_library_page_v3.dart';

/// 现代化物料卡片组件 V2
/// 支持多种视图类型、响应式布局和现代化交互效果
class MaterialCardV2 extends ConsumerStatefulWidget {
  final domain.Material material;
  final MaterialCardViewType viewType;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onAddToBom;
  final bool showActions;

  const MaterialCardV2({
    super.key,
    required this.material,
    required this.viewType,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onAddToBom,
    this.showActions = true,
  });

  @override
  ConsumerState<MaterialCardV2> createState() => _MaterialCardV2State();
}

class _MaterialCardV2State extends ConsumerState<MaterialCardV2>
    with TickerProviderStateMixin {
  
  late AnimationController _hoverController;
  late AnimationController _pressController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  
  bool _isHovered = false;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _pressController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _pressController,
      curve: Curves.easeInOut,
    ));
    
    _elevationAnimation = Tween<double>(
      begin: 2.0,
      end: 8.0,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _pressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    switch (widget.viewType) {
      case MaterialCardViewType.grid:
        return _buildGridCard(context);
      case MaterialCardViewType.list:
        return _buildListCard(context);
      case MaterialCardViewType.compact:
        return _buildCompactCard(context);
    }
  }

  /// 构建网格卡片
  Widget _buildGridCard(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_hoverController, _pressController]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: MouseRegion(
            onEnter: (_) => _handleHover(true),
            onExit: (_) => _handleHover(false),
            child: GestureDetector(
              onTapDown: (_) => _handlePress(true),
              onTapUp: (_) => _handlePress(false),
              onTapCancel: () => _handlePress(false),
              onTap: widget.onTap,
              child: Card(
                elevation: _elevationAnimation.value,
                shadowColor: VanHubColors.shadow.withOpacity(0.3),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                  side: BorderSide(
                    color: _isHovered 
                        ? VanHubColors.primary.withOpacity(0.3)
                        : Colors.transparent,
                    width: 2,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildImageSection(),
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.all(VanHubSpacing.md),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildTitleSection(),
                              const SizedBox(height: VanHubSpacing.xs),
                              _buildSpecsSection(),
                              const Spacer(),
                              _buildPriceSection(),
                              if (widget.showActions) ...[
                                const SizedBox(height: VanHubSpacing.sm),
                                _buildActionButtons(),
                              ],
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建列表卡片
  Widget _buildListCard(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_hoverController, _pressController]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: MouseRegion(
            onEnter: (_) => _handleHover(true),
            onExit: (_) => _handleHover(false),
            child: GestureDetector(
              onTapDown: (_) => _handlePress(true),
              onTapUp: (_) => _handlePress(false),
              onTapCancel: () => _handlePress(false),
              onTap: widget.onTap,
              child: Card(
                elevation: _elevationAnimation.value,
                shadowColor: VanHubColors.shadow.withOpacity(0.3),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(
                    color: _isHovered 
                        ? VanHubColors.primary.withOpacity(0.3)
                        : Colors.transparent,
                    width: 2,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: IntrinsicHeight(
                    child: Row(
                      children: [
                        _buildListImageSection(),
                        Expanded(
                          child: Padding(
                            padding: EdgeInsets.all(VanHubSpacing.md),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Expanded(child: _buildTitleSection()),
                                    _buildCategoryBadge(),
                                  ],
                                ),
                                const SizedBox(height: VanHubSpacing.xs),
                                _buildSpecsSection(),
                                const SizedBox(height: VanHubSpacing.sm),
                                Row(
                                  children: [
                                    Expanded(child: _buildPriceSection()),
                                    if (widget.showActions) _buildListActionButtons(),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建紧凑卡片
  Widget _buildCompactCard(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_hoverController, _pressController]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: MouseRegion(
            onEnter: (_) => _handleHover(true),
            onExit: (_) => _handleHover(false),
            child: GestureDetector(
              onTapDown: (_) => _handlePress(true),
              onTapUp: (_) => _handlePress(false),
              onTapCancel: () => _handlePress(false),
              onTap: widget.onTap,
              child: Card(
                elevation: _elevationAnimation.value,
                shadowColor: VanHubColors.shadow.withOpacity(0.3),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(
                    color: _isHovered 
                        ? VanHubColors.primary.withOpacity(0.3)
                        : Colors.transparent,
                    width: 1,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Padding(
                    padding: EdgeInsets.all(VanHubSpacing.sm),
                    child: Row(
                      children: [
                        _buildCompactImageSection(),
                        const SizedBox(width: VanHubSpacing.sm),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Expanded(child: _buildCompactTitleSection()),
                                  _buildCompactPriceSection(),
                                ],
                              ),
                              const SizedBox(height: VanHubSpacing.xs),
                              _buildCompactSpecsSection(),
                            ],
                          ),
                        ),
                        if (widget.showActions) ...[
                          const SizedBox(width: VanHubSpacing.sm),
                          _buildCompactActionButtons(),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 处理悬停状态
  void _handleHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });
    if (isHovered) {
      _hoverController.forward();
    } else {
      _hoverController.reverse();
    }
  }

  /// 处理按压状态
  void _handlePress(bool isPressed) {
    setState(() {
      _isPressed = isPressed;
    });
    if (isPressed) {
      _pressController.forward();
    } else {
      _pressController.reverse();
    }
  }

  // ============================================================================
  // 组件构建方法 (Component Building Methods)
  // ============================================================================

  /// 构建图片区域（网格视图）
  Widget _buildImageSection() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            VanHubColors.primary.withOpacity(0.8),
            VanHubColors.primary,
          ],
        ),
      ),
      child: Stack(
        children: [
          // 背景图片或默认图案
          if (widget.material.imageUrl != null)
            Image.network(
              widget.material.imageUrl!,
              width: double.infinity,
              height: 120,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => _buildDefaultImage(),
            )
          else
            _buildDefaultImage(),

          // 渐变遮罩
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withOpacity(0.3),
                ],
              ),
            ),
          ),

          // 分类标签
          Positioned(
            top: VanHubSpacing.sm,
            right: VanHubSpacing.sm,
            child: _buildCategoryBadge(),
          ),
        ],
      ),
    );
  }

  /// 构建列表图片区域
  Widget _buildListImageSection() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            VanHubColors.primary.withOpacity(0.8),
            VanHubColors.primary,
          ],
        ),
      ),
      child: widget.material.imageUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                widget.material.imageUrl!,
                width: 80,
                height: 80,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildSmallDefaultImage(),
              ),
            )
          : _buildSmallDefaultImage(),
    );
  }

  /// 构建紧凑图片区域
  Widget _buildCompactImageSection() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            VanHubColors.primary.withOpacity(0.8),
            VanHubColors.primary,
          ],
        ),
      ),
      child: widget.material.imageUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(6),
              child: Image.network(
                widget.material.imageUrl!,
                width: 48,
                height: 48,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildTinyDefaultImage(),
              ),
            )
          : _buildTinyDefaultImage(),
    );
  }

  /// 构建默认图片（大）
  Widget _buildDefaultImage() {
    return Container(
      width: double.infinity,
      height: 120,
      child: Stack(
        children: [
          // 背景图案
          Positioned(
            right: -20,
            top: -10,
            child: Icon(
              Icons.inventory_2_outlined,
              size: 80,
              color: VanHubColors.onPrimary.withOpacity(0.2),
            ),
          ),
          // 材料图标
          Center(
            child: Icon(
              _getCategoryIcon(widget.material.category),
              size: 40,
              color: VanHubColors.onPrimary,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建默认图片（中）
  Widget _buildSmallDefaultImage() {
    return Center(
      child: Icon(
        _getCategoryIcon(widget.material.category),
        size: 32,
        color: VanHubColors.onPrimary,
      ),
    );
  }

  /// 构建默认图片（小）
  Widget _buildTinyDefaultImage() {
    return Center(
      child: Icon(
        _getCategoryIcon(widget.material.category),
        size: 24,
        color: VanHubColors.onPrimary,
      ),
    );
  }

  /// 构建标题区域
  Widget _buildTitleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.material.name,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: VanHubColors.onSurface,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        if (widget.material.brand != null) ...[
          const SizedBox(height: VanHubSpacing.xs),
          Text(
            widget.material.brand!,
            style: TextStyle(
              fontSize: 12,
              color: VanHubColors.onSurface.withOpacity(0.7),
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  /// 构建紧凑标题区域
  Widget _buildCompactTitleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.material.name,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: VanHubColors.onSurface,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        if (widget.material.brand != null)
          Text(
            widget.material.brand!,
            style: TextStyle(
              fontSize: 11,
              color: VanHubColors.onSurface.withOpacity(0.6),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
      ],
    );
  }

  /// 构建规格区域
  Widget _buildSpecsSection() {
    if (widget.material.specifications == null) return const SizedBox.shrink();

    return Text(
      widget.material.specifications!,
      style: TextStyle(
        fontSize: 12,
        color: VanHubColors.onSurface.withOpacity(0.6),
        height: 1.3,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// 构建紧凑规格区域
  Widget _buildCompactSpecsSection() {
    if (widget.material.specifications == null) return const SizedBox.shrink();

    return Text(
      widget.material.specifications!,
      style: TextStyle(
        fontSize: 11,
        color: VanHubColors.onSurface.withOpacity(0.5),
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// 构建价格区域
  Widget _buildPriceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '¥${_formatPrice(widget.material.price)}',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: VanHubColors.primary,
          ),
        ),
        if (widget.material.purchaseDate != null)
          Text(
            _formatDate(widget.material.purchaseDate!),
            style: TextStyle(
              fontSize: 10,
              color: VanHubColors.onSurface.withOpacity(0.5),
            ),
          ),
      ],
    );
  }

  /// 构建紧凑价格区域
  Widget _buildCompactPriceSection() {
    return Text(
      '¥${_formatPrice(widget.material.price)}',
      style: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.bold,
        color: VanHubColors.primary,
      ),
    );
  }

  /// 构建分类标签
  Widget _buildCategoryBadge() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: VanHubSpacing.sm,
        vertical: VanHubSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: VanHubColors.primaryContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        widget.material.category,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w600,
          color: VanHubColors.onPrimaryContainer,
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: _buildActionButton(
            icon: Icons.add_shopping_cart_outlined,
            label: 'BOM',
            onPressed: widget.onAddToBom,
            isPrimary: true,
          ),
        ),
        const SizedBox(width: VanHubSpacing.xs),
        _buildIconButton(
          icon: Icons.edit_outlined,
          onPressed: widget.onEdit,
          tooltip: '编辑',
        ),
        const SizedBox(width: VanHubSpacing.xs),
        _buildIconButton(
          icon: Icons.delete_outline,
          onPressed: widget.onDelete,
          tooltip: '删除',
          isDestructive: true,
        ),
      ],
    );
  }

  /// 构建列表操作按钮
  Widget _buildListActionButtons() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildIconButton(
          icon: Icons.add_shopping_cart_outlined,
          onPressed: widget.onAddToBom,
          tooltip: '添加到BOM',
        ),
        const SizedBox(width: VanHubSpacing.xs),
        _buildIconButton(
          icon: Icons.edit_outlined,
          onPressed: widget.onEdit,
          tooltip: '编辑',
        ),
        const SizedBox(width: VanHubSpacing.xs),
        _buildIconButton(
          icon: Icons.delete_outline,
          onPressed: widget.onDelete,
          tooltip: '删除',
          isDestructive: true,
        ),
      ],
    );
  }

  /// 构建紧凑操作按钮
  Widget _buildCompactActionButtons() {
    return PopupMenuButton<String>(
      icon: Icon(
        Icons.more_vert,
        size: 18,
        color: VanHubColors.onSurface.withOpacity(0.7),
      ),
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'bom',
          child: Row(
            children: [
              Icon(Icons.add_shopping_cart_outlined, size: 16),
              const SizedBox(width: VanHubSpacing.sm),
              const Text('添加到BOM'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit_outlined, size: 16),
              const SizedBox(width: VanHubSpacing.sm),
              const Text('编辑'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete_outline, size: 16, color: VanHubColors.error),
              const SizedBox(width: VanHubSpacing.sm),
              Text('删除', style: TextStyle(color: VanHubColors.error)),
            ],
          ),
        ),
      ],
      onSelected: (value) {
        switch (value) {
          case 'bom':
            widget.onAddToBom?.call();
            break;
          case 'edit':
            widget.onEdit?.call();
            break;
          case 'delete':
            widget.onDelete?.call();
            break;
        }
      },
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
    bool isPrimary = false,
  }) {
    return SizedBox(
      height: 32,
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon, size: 16),
        label: Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: isPrimary ? VanHubColors.primary : VanHubColors.surface,
          foregroundColor: isPrimary ? VanHubColors.onPrimary : VanHubColors.onSurface,
          elevation: 0,
          padding: EdgeInsets.symmetric(horizontal: VanHubSpacing.sm),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
            side: BorderSide(
              color: isPrimary ? Colors.transparent : VanHubColors.outline,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建图标按钮
  Widget _buildIconButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required String tooltip,
    bool isDestructive = false,
  }) {
    return SizedBox(
      width: 32,
      height: 32,
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(
          icon,
          size: 16,
          color: isDestructive ? VanHubColors.error : VanHubColors.onSurface.withOpacity(0.7),
        ),
        tooltip: tooltip,
        style: IconButton.styleFrom(
          backgroundColor: VanHubColors.surface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
            side: BorderSide(color: VanHubColors.outline),
          ),
        ),
      ),
    );
  }

  // ============================================================================
  // 辅助方法 (Helper Methods)
  // ============================================================================

  /// 格式化价格
  String _formatPrice(double price) {
    final formatter = NumberFormat('#,##0.00', 'zh_CN');
    return formatter.format(price);
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    final formatter = DateFormat('yyyy-MM-dd', 'zh_CN');
    return formatter.format(date);
  }

  /// 获取分类图标
  IconData _getCategoryIcon(String category) {
    switch (category) {
      case '电路系统':
        return Icons.electrical_services;
      case '水路系统':
        return Icons.water_drop;
      case '床铺系统':
        return Icons.bed;
      case '储物系统':
        return Icons.storage;
      case '厨房系统':
        return Icons.kitchen;
      case '卫浴系统':
        return Icons.bathroom;
      case '底盘改装':
        return Icons.build;
      case '外观改装':
        return Icons.palette;
      default:
        return Icons.inventory_2;
    }
  }
}
