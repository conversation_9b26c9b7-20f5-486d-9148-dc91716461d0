import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/create_project_request.dart';
import '../../domain/services/project_service.dart';
import '../providers/project_ui_state_provider.dart';

/// 创建项目对话框
/// 严格遵循Clean Architecture原则，只负责UI展示和用户交互
/// 通过ProjectProvider调用Domain层的UseCase
class CreateProjectDialogWidget extends ConsumerStatefulWidget {
  const CreateProjectDialogWidget({super.key});

  @override
  ConsumerState<CreateProjectDialogWidget> createState() => _CreateProjectDialogWidgetState();
}

class _CreateProjectDialogWidgetState extends ConsumerState<CreateProjectDialogWidget> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _vehicleBrandController = TextEditingController();
  final _vehicleModelController = TextEditingController();
  final _budgetController = TextEditingController();

  String _selectedVehicleType = '自行式房车';
  bool _isPublic = true;
  bool _isLoading = false;
  List<String> _selectedSystems = [];

  // 房车类型选项
  final List<String> _vehicleTypes = [
    '自行式房车',
    '拖挂式房车',
    '皮卡改装',
    '面包车改装',
    '货车改装',
    '其他车型',
  ];

  // 改装系统模板
  final Map<String, Map<String, dynamic>> _systemTemplates = {
    '电路系统': {
      'icon': Icons.electrical_services,
      'color': Colors.amber,
      'description': '电池、逆变器、充电系统、照明等',
    },
    '水路系统': {
      'icon': Icons.water_drop,
      'color': Colors.blue,
      'description': '水箱、水泵、净水器、热水器等',
    },
    '储物系统': {
      'icon': Icons.storage,
      'color': Colors.green,
      'description': '橱柜、抽屉、储物箱、挂钩等',
    },
    '床铺系统': {
      'icon': Icons.bed,
      'color': Colors.purple,
      'description': '床垫、床架、升降床、沙发床等',
    },
    '厨房系统': {
      'icon': Icons.kitchen,
      'color': Colors.orange,
      'description': '灶具、冰箱、微波炉、橱柜等',
    },
    '卫浴系统': {
      'icon': Icons.bathroom,
      'color': Colors.teal,
      'description': '马桶、洗手盆、淋浴、排风等',
    },
    '外观改装': {
      'icon': Icons.brush,
      'color': Colors.red,
      'description': '车身贴纸、遮阳棚、行李架等',
    },
    '底盘改装': {
      'icon': Icons.build,
      'color': Colors.grey,
      'description': '悬挂、轮胎、刹车、防护等',
    },
  };

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _vehicleBrandController.dispose();
    _vehicleModelController.dispose();
    _budgetController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题栏
            Row(
              children: [
                const Icon(
                  Icons.add_road,
                  color: Colors.deepOrange,
                  size: 28,
                ),
                const SizedBox(width: 12),
                const Text(
                  '开始新的改装项目',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // 表单内容
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildBasicInfoSection(),
                      const SizedBox(height: 24),
                      _buildVehicleInfoSection(),
                      const SizedBox(height: 24),
                      _buildSystemTemplatesSection(),
                      const SizedBox(height: 24),
                      _buildProjectSettingsSection(),
                    ],
                  ),
                ),
              ),
            ),

            // 底部按钮
            const SizedBox(height: 24),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// 构建基本信息部分
  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '项目基本信息',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),

        // 项目标题
        TextFormField(
          controller: _titleController,
          decoration: const InputDecoration(
            labelText: '项目标题 *',
            hintText: '例如：2023款大通V90房车改装',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.title),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入项目标题';
            }
            if (value.trim().length < 3) {
              return '项目标题至少需要3个字符（当前${value.trim().length}个字符）';
            }
            if (value.trim().length > 50) {
              return '项目标题不能超过50个字符';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // 项目描述
        TextFormField(
          controller: _descriptionController,
          maxLines: 3,
          decoration: const InputDecoration(
            labelText: '项目描述',
            hintText: '描述您的改装计划、目标和特色...',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.description),
          ),
          validator: (value) {
            if (value != null && value.trim().length > 500) {
              return '描述不能超过500个字符';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // 预算
        TextFormField(
          controller: _budgetController,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: '预算金额（元）',
            hintText: '例如：50000',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.attach_money),
          ),
          validator: (value) {
            if (value != null && value.trim().isNotEmpty) {
              final budget = double.tryParse(value.trim());
              if (budget == null || budget < 0) {
                return '请输入有效的预算金额';
              }
              // 限制最大预算为999万（9,999,999.99）
              if (budget > 9999999.99) {
                return '预算金额不能超过999万元';
              }
            }
            return null;
          },
        ),
      ],
    );
  }

  /// 构建车辆信息部分
  Widget _buildVehicleInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '车辆信息',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),

        // 车辆类型选择
        DropdownButtonFormField<String>(
          value: _selectedVehicleType,
          decoration: const InputDecoration(
            labelText: '车辆类型 *',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.directions_car),
          ),
          items: _vehicleTypes.map((type) {
            return DropdownMenuItem(
              value: type,
              child: Text(type),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedVehicleType = value;
              });
            }
          },
        ),
        const SizedBox(height: 16),

        // 车辆品牌和型号
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _vehicleBrandController,
                decoration: const InputDecoration(
                  labelText: '车辆品牌',
                  hintText: '例如：大通',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextFormField(
                controller: _vehicleModelController,
                decoration: const InputDecoration(
                  labelText: '车辆型号',
                  hintText: '例如：V90（可选）',
                  border: OutlineInputBorder(),
                ),
                // 移除必填验证，让车辆型号变为可选
                // validator: (value) {
                //   if (value == null || value.trim().isEmpty) {
                //     return '请输入车辆型号';
                //   }
                //   return null;
                // },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建改装系统模板选择部分
  Widget _buildSystemTemplatesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '选择改装系统',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '选择您计划改装的系统，可以多选',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 16),

        // 系统模板网格
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 3,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
          ),
          itemCount: _systemTemplates.length,
          itemBuilder: (context, index) {
            final systemName = _systemTemplates.keys.elementAt(index);
            final systemData = _systemTemplates[systemName]!;
            final isSelected = _selectedSystems.contains(systemName);

            return InkWell(
              onTap: () {
                setState(() {
                  if (isSelected) {
                    _selectedSystems.remove(systemName);
                  } else {
                    _selectedSystems.add(systemName);
                  }
                });
              },
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isSelected
                      ? systemData['color'].withValues(alpha: 0.1)
                      : Colors.grey[50],
                  border: Border.all(
                    color: isSelected
                        ? systemData['color']
                        : Colors.grey[300]!,
                    width: isSelected ? 2 : 1,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      systemData['icon'],
                      color: isSelected
                          ? systemData['color']
                          : Colors.grey[600],
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        systemName,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: isSelected
                              ? FontWeight.w600
                              : FontWeight.normal,
                          color: isSelected
                              ? systemData['color']
                              : Colors.grey[700],
                        ),
                      ),
                    ),
                    if (isSelected)
                      Icon(
                        Icons.check_circle,
                        color: systemData['color'],
                        size: 16,
                      ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  /// 构建项目设置部分
  Widget _buildProjectSettingsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '项目设置',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),

        // 公开设置
        SwitchListTile(
          title: const Text('公开项目'),
          subtitle: Text(
            _isPublic
                ? '其他用户可以查看和复刻您的项目'
                : '项目仅对您可见',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
          value: _isPublic,
          onChanged: (value) {
            setState(() {
              _isPublic = value;
            });
          },
          activeColor: Colors.deepOrange,
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _createProject,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.deepOrange,
              foregroundColor: Colors.white,
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('创建项目'),
          ),
        ),
      ],
    );
  }

  /// 创建项目 - 严格遵循Clean Architecture原则
  /// 只负责UI逻辑，通过Provider调用Domain层UseCase
  Future<void> _createProject() async {
    // 防重复调用保护
    if (_isLoading) {
      debugPrint('🔍 调试：项目创建正在进行中，忽略重复调用');
      return;
    }

    debugPrint('🔍 调试：开始创建项目流程');

    // 详细检查每个字段的值
    debugPrint('🔍 调试：表单字段值检查：');
    debugPrint('  - 项目标题: "${_titleController.text.trim()}"');
    debugPrint('  - 项目描述: "${_descriptionController.text.trim()}"');
    debugPrint('  - 车辆类型: "$_selectedVehicleType"');
    debugPrint('  - 车辆品牌: "${_vehicleBrandController.text.trim()}"');
    debugPrint('  - 车辆型号: "${_vehicleModelController.text.trim()}"');
    debugPrint('  - 预算: "${_budgetController.text.trim()}"');
    debugPrint('  - 选中系统: $_selectedSystems');

    if (!_formKey.currentState!.validate()) {
      debugPrint('🔍 调试：表单验证失败');
      debugPrint('🔍 调试：请检查以下必填字段：');
      debugPrint('  - 项目标题（至少3个字符）');
      debugPrint('  - 车辆型号（必填）');

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请检查表单中的必填字段'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    debugPrint('🔍 调试：表单验证通过，开始创建项目');
    setState(() {
      _isLoading = true;
    });

    try {
      // 构建CreateProjectRequest - Domain层实体
      final vehicleBrand = _vehicleBrandController.text.trim();
      final vehicleModel = _vehicleModelController.text.trim();

      // vehicleModel现在是必需字段，组合品牌和型号
      final combinedVehicleModel = vehicleBrand.isEmpty
          ? vehicleModel
          : '$vehicleBrand $vehicleModel';

      final request = CreateProjectRequest(
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? '暂无描述'
            : _descriptionController.text.trim(),
        vehicleType: _selectedVehicleType,
        vehicleModel: combinedVehicleModel,
        vehicleBrand: vehicleBrand.isEmpty ? null : vehicleBrand,
        budget: _budgetController.text.trim().isEmpty
            ? 0.0
            : (double.tryParse(_budgetController.text.trim()) ?? 0.0),
        isPublic: _isPublic,
        tags: _selectedSystems,
      );

      debugPrint('🔍 调试：创建项目请求数据 = ${request.toString()}');

      // 设置UI加载状态
      ref.read(projectCreateUiStateProvider.notifier).setLoading();

      // 通过纯业务逻辑服务调用Domain层UseCase
      debugPrint('🔍 调试：准备调用projectService');
      final result = await ref.read(projectServiceProvider.notifier).createProject(request);
      debugPrint('🔍 调试：projectService调用完成，结果 = ${result.isRight() ? "成功" : "失败"}');

      if (mounted) {
        result.fold(
          (failure) {
            debugPrint('🔍 调试：创建项目失败，错误信息 = ${failure.message}');

            // 设置UI错误状态
            ref.read(projectCreateUiStateProvider.notifier).setError(failure.message);

            // 显示错误信息
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('创建项目失败: ${failure.message}'),
                backgroundColor: Colors.red,
                action: SnackBarAction(
                  label: '重试',
                  textColor: Colors.white,
                  onPressed: _createProject,
                ),
              ),
            );
          },
          (project) {
            debugPrint('🔍 调试：创建项目成功，项目信息 = ${project.toString()}');

            // 设置UI成功状态
            ref.read(projectCreateUiStateProvider.notifier).setSuccess('项目创建成功');

            // 创建成功
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('项目 "${project.title}" 创建成功！'),
                backgroundColor: Colors.green,
                action: SnackBarAction(
                  label: '查看',
                  textColor: Colors.white,
                  onPressed: () {
                    // TODO: 导航到项目详情页面
                  },
                ),
              ),
            );

            // 关闭对话框，返回true表示创建成功
            Navigator.of(context).pop(true);
          },
        );
      }
    } catch (e) {
      debugPrint('🔍 调试：创建项目时发生异常 = $e');

      // 设置UI错误状态
      ref.read(projectCreateUiStateProvider.notifier).setError('创建项目时发生错误: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('创建项目时发生错误: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        // 重置本地UI状态
        setState(() {
          _isLoading = false;
        });

        // 如果没有错误或成功消息，重置UI状态为空闲
        final uiState = ref.read(projectCreateUiStateProvider);
        if (!uiState.hasMessage) {
          ref.read(projectCreateUiStateProvider.notifier).reset();
        }
      }
    }
  }
}