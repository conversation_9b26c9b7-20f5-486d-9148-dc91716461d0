import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/widgets/loading_widget.dart';
import '../../../../core/widgets/error_widget.dart';
import '../../../../core/widgets/empty_state_widget.dart';
import '../providers/project_provider.dart';
import '../widgets/project_card_widget.dart';
import '../widgets/create_project_dialog_widget.dart';

class ProjectListPage extends ConsumerWidget {
  final bool showPublicProjects;
  final String? userId;

  const ProjectListPage({
    super.key,
    this.showPublicProjects = false,
    this.userId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final projectsAsync = showPublicProjects
        ? ref.watch(publicProjectsProvider())
        : userId != null
            ? ref.watch(userProjectsProvider(userId!))
            : ref.watch(publicProjectsProvider());

    return Scaffold(
      appBar: AppBar(
        title: Text(showPublicProjects ? '发现项目' : '我的项目'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (!showPublicProjects)
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => _showCreateProjectDialog(context, ref),
            ),
        ],
      ),
      body: projectsAsync.when(
        data: (projects) {
          if (projects.isEmpty) {
            return EmptyStateWidget(
              icon: Icons.folder_open,
              title: showPublicProjects ? '暂无公开项目' : '还没有项目',
              subtitle: showPublicProjects ? '成为第一个分享项目的人' : '创建您的第一个改装项目',
              actionText: showPublicProjects ? null : '创建项目',
              onAction: showPublicProjects ? null : () => _showCreateProjectDialog(context, ref),
            );
          }

          return RefreshIndicator(
            onRefresh: () async {
              if (showPublicProjects) {
                ref.invalidate(publicProjectsProvider());
              } else if (userId != null) {
                ref.invalidate(userProjectsProvider(userId!));
              }
              // Wait a bit for the invalidation to take effect
              await Future.delayed(const Duration(milliseconds: 100));
            },
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: projects.length,
              itemBuilder: (context, index) {
                final project = projects[index];
                return ProjectCardWidget(
                  project: project,
                  onTap: () => _navigateToProjectDetail(context, project.id),
                  showActions: !showPublicProjects,
                );
              },
            ),
          );
        },
        loading: () => const LoadingWidget(message: '加载项目中...'),
        error: (error, stack) => ErrorDisplayWidget(
          message: '加载项目失败: $error',
          onRetry: () {
            if (showPublicProjects) {
              ref.invalidate(publicProjectsProvider());
            } else if (userId != null) {
              ref.invalidate(userProjectsProvider(userId!));
            }
          },
        ),
      ),
    );
  }

  void _showCreateProjectDialog(BuildContext context, WidgetRef ref) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => const CreateProjectDialogWidget(),
    );
    
    // 如果创建成功，刷新项目列表
    if (result == true) {
      if (showPublicProjects) {
        ref.invalidate(publicProjectsProvider());
      } else if (userId != null) {
        ref.invalidate(userProjectsProvider(userId!));
      } else {
        ref.invalidate(publicProjectsProvider());
      }
    }
  }

  void _navigateToProjectDetail(BuildContext context, String projectId) {
    Navigator.of(context).pushNamed('/project/$projectId');
  }
}