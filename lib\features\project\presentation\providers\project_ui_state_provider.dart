import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'project_ui_state_provider.g.dart';

/// 项目UI状态 - 专门管理UI层的加载和错误状态
class ProjectUiState {
  final bool isLoading;
  final String? errorMessage;
  final String? successMessage;
  final DateTime? lastUpdated;

  const ProjectUiState({
    this.isLoading = false,
    this.errorMessage,
    this.successMessage,
    this.lastUpdated,
  });

  ProjectUiState copyWith({
    bool? isLoading,
    String? errorMessage,
    String? successMessage,
    DateTime? lastUpdated,
  }) {
    return ProjectUiState(
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
      successMessage: successMessage,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// 创建加载状态
  ProjectUiState toLoading() {
    return copyWith(
      isLoading: true,
      errorMessage: null,
      successMessage: null,
      lastUpdated: DateTime.now(),
    );
  }

  /// 创建成功状态
  ProjectUiState toSuccess(String message) {
    return copyWith(
      isLoading: false,
      errorMessage: null,
      successMessage: message,
      lastUpdated: DateTime.now(),
    );
  }

  /// 创建错误状态
  ProjectUiState toError(String message) {
    return copyWith(
      isLoading: false,
      errorMessage: message,
      successMessage: null,
      lastUpdated: DateTime.now(),
    );
  }

  /// 创建空闲状态
  ProjectUiState toIdle() {
    return copyWith(
      isLoading: false,
      errorMessage: null,
      successMessage: null,
      lastUpdated: DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'ProjectUiState(isLoading: $isLoading, errorMessage: $errorMessage, successMessage: $successMessage)';
  }
}

/// 项目创建UI状态Provider - 专门管理项目创建的UI状态
@riverpod
class ProjectCreateUiState extends _$ProjectCreateUiState {
  @override
  ProjectUiState build() {
    return const ProjectUiState();
  }

  /// 设置加载状态
  void setLoading() {
    state = state.toLoading();
  }

  /// 设置成功状态
  void setSuccess(String message) {
    state = state.toSuccess(message);
  }

  /// 设置错误状态
  void setError(String message) {
    state = state.toError(message);
  }

  /// 重置为空闲状态
  void reset() {
    state = state.toIdle();
  }

  /// 清除消息（保持其他状态不变）
  void clearMessages() {
    state = state.copyWith(
      errorMessage: null,
      successMessage: null,
    );
  }
}

/// 项目复刻UI状态Provider
@riverpod
class ProjectForkUiState extends _$ProjectForkUiState {
  @override
  ProjectUiState build() {
    return const ProjectUiState();
  }

  /// 设置加载状态
  void setLoading() {
    state = state.toLoading();
  }

  /// 设置成功状态
  void setSuccess(String message) {
    state = state.toSuccess(message);
  }

  /// 设置错误状态
  void setError(String message) {
    state = state.toError(message);
  }

  /// 重置为空闲状态
  void reset() {
    state = state.toIdle();
  }
}

/// 项目列表UI状态Provider
@riverpod
class ProjectListUiState extends _$ProjectListUiState {
  @override
  ProjectUiState build() {
    return const ProjectUiState();
  }

  /// 设置刷新状态
  void setRefreshing() {
    state = state.toLoading();
  }

  /// 设置刷新成功
  void setRefreshSuccess() {
    state = state.toSuccess('项目列表已更新');
  }

  /// 设置刷新错误
  void setRefreshError(String message) {
    state = state.toError(message);
  }

  /// 重置状态
  void reset() {
    state = state.toIdle();
  }
}

/// UI状态工具类 - 提供常用的状态检查方法
extension ProjectUiStateExtension on ProjectUiState {
  /// 是否有错误
  bool get hasError => errorMessage != null;

  /// 是否有成功消息
  bool get hasSuccess => successMessage != null;

  /// 是否有任何消息
  bool get hasMessage => hasError || hasSuccess;

  /// 是否处于空闲状态
  bool get isIdle => !isLoading && !hasMessage;

  /// 获取显示消息
  String? get displayMessage => errorMessage ?? successMessage;
}

/// 全局项目操作状态Provider - 用于跨组件的状态共享
@riverpod
class GlobalProjectOperationState extends _$GlobalProjectOperationState {
  @override
  ProjectUiState build() {
    return const ProjectUiState();
  }

  /// 开始全局操作
  void startOperation(String operationType) {
    state = state.toLoading();
  }

  /// 完成全局操作
  void completeOperation(String message) {
    state = state.toSuccess(message);
    
    // 3秒后自动清除成功消息
    Future.delayed(const Duration(seconds: 3), () {
      if (state.successMessage == message) {
        reset();
      }
    });
  }

  /// 操作失败
  void failOperation(String message) {
    state = state.toError(message);
    
    // 5秒后自动清除错误消息
    Future.delayed(const Duration(seconds: 5), () {
      if (state.errorMessage == message) {
        reset();
      }
    });
  }

  /// 重置状态
  void reset() {
    state = state.toIdle();
  }
}
