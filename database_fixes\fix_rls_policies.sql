-- VanHub数据库RLS策略修复脚本
-- 执行日期：2025-01-27
-- 目的：修复项目数据读取权限问题，确保游客模式和用户都能正常访问数据

-- ========================================
-- 第一部分：检查当前RLS策略状态
-- ========================================

-- 检查projects表的RLS状态
SELECT
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables
WHERE tablename = 'projects' AND schemaname = 'public';

-- 查看当前的RLS策略
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'projects' AND schemaname = 'public';

-- ========================================
-- 第二部分：修复projects表的RLS策略
-- ========================================

-- 删除可能存在的有问题的策略
DROP POLICY IF EXISTS "Enable read access for all users" ON public.projects;
DROP POLICY IF EXISTS "Enable read access for authenticated users only" ON public.projects;
DROP POLICY IF EXISTS "Enable read access for public projects" ON public.projects;
DROP POLICY IF EXISTS "Users can view public projects" ON public.projects;
DROP POLICY IF EXISTS "Public projects are viewable by everyone" ON public.projects;

-- 创建新的读取策略：允许所有人（包括游客）读取公开项目
CREATE POLICY "Allow public read access to public projects" ON public.projects
    FOR SELECT
    USING (is_public = true);

-- 创建用户项目读取策略：用户可以读取自己的所有项目
CREATE POLICY "Users can read own projects" ON public.projects
    FOR SELECT
    USING (auth.uid() = user_id);

-- 创建项目写入策略：只有认证用户可以创建项目
CREATE POLICY "Authenticated users can insert projects" ON public.projects
    FOR INSERT
    WITH CHECK (auth.uid() = user_id);

-- 创建项目更新策略：用户只能更新自己的项目
CREATE POLICY "Users can update own projects" ON public.projects
    FOR UPDATE
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- 创建项目删除策略：用户只能删除自己的项目
CREATE POLICY "Users can delete own projects" ON public.projects
    FOR DELETE
    USING (auth.uid() = user_id);

-- ========================================
-- 第三部分：确保RLS已启用
-- ========================================

-- 启用RLS（如果尚未启用）
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;

-- ========================================
-- 第四部分：验证策略是否正确
-- ========================================

-- 查看修复后的RLS策略
SELECT 
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'projects' AND schemaname = 'public'
ORDER BY policyname;

-- ========================================
-- 第五部分：测试数据访问
-- ========================================

-- 测试公开项目查询（应该返回所有公开项目）
SELECT 
    id,
    title,
    is_public,
    user_id,
    created_at
FROM public.projects 
WHERE is_public = true 
ORDER BY created_at DESC 
LIMIT 5;

-- 统计公开项目数量
SELECT 
    COUNT(*) as total_projects,
    COUNT(CASE WHEN is_public = true THEN 1 END) as public_projects,
    COUNT(CASE WHEN is_public = false THEN 1 END) as private_projects
FROM public.projects;

-- ========================================
-- 第六部分：修复相关视图的权限（如果存在）
-- ========================================

-- 如果存在project_bom_summary视图，也需要修复其权限
DO $$ 
BEGIN
    -- 检查视图是否存在
    IF EXISTS (
        SELECT 1 
        FROM information_schema.views 
        WHERE table_name = 'project_bom_summary' 
        AND table_schema = 'public'
    ) THEN
        -- 为视图启用RLS
        ALTER VIEW public.project_bom_summary SET (security_barrier = true);
        
        -- 删除可能存在的有问题的策略
        DROP POLICY IF EXISTS "Enable read access for project_bom_summary" ON public.project_bom_summary;
        
        RAISE NOTICE 'project_bom_summary视图权限已修复';
    ELSE
        RAISE NOTICE 'project_bom_summary视图不存在，跳过修复';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '修复project_bom_summary视图时出错: %', SQLERRM;
END $$;

-- ========================================
-- 第七部分：最终验证
-- ========================================

-- 显示修复结果摘要
SELECT 
    '=== RLS策略修复完成 ===' as status,
    COUNT(*) as total_policies
FROM pg_policies 
WHERE tablename = 'projects' AND schemaname = 'public';

-- 提示信息
SELECT 
    '修复完成！现在应该可以正常访问项目数据了。' as message,
    '游客可以查看公开项目，用户可以查看自己的所有项目。' as note;
