import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/create_project_dialog_widget.dart';
import '../providers/project_provider.dart';
import '../../domain/entities/create_project_request.dart';
import '../../../../core/providers/auth_state_provider.dart';

/// 测试创建项目功能的页面
class TestCreateProjectPage extends ConsumerStatefulWidget {
  const TestCreateProjectPage({Key? key}) : super(key: key);

  @override
  ConsumerState<TestCreateProjectPage> createState() => _TestCreateProjectPageState();
}

class _TestCreateProjectPageState extends ConsumerState<TestCreateProjectPage> {
  String? _lastResult;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final currentUserId = ref.watch(currentUserIdProvider);
    final projectController = ref.watch(projectControllerProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('测试创建项目'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户状态
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '用户状态',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    currentUserId != null
                        ? Text('已登录: $currentUserId', style: const TextStyle(color: Colors.green))
                        : const Text('未登录', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Provider状态
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Provider状态',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    projectController.when(
                      data: (_) => const Text('就绪', style: TextStyle(color: Colors.green)),
                      loading: () => const Text('加载中...', style: TextStyle(color: Colors.orange)),
                      error: (error, stack) => Text('错误: $error', style: const TextStyle(color: Colors.red)),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // 测试按钮
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _testCreateProjectDialog,
                    icon: const Icon(Icons.add_road),
                    label: const Text('测试创建项目对话框'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _testDirectCreate,
                    icon: const Icon(Icons.code),
                    label: const Text('测试直接创建'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Tags字段测试按钮
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _testTagsField,
                icon: _isLoading
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.label),
                label: Text(_isLoading ? '测试中...' : '🔧 测试Tags字段修复'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // 结果显示
            if (_lastResult != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '最后结果',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Text(_lastResult!),
                    ],
                  ),
                ),
              ),
            ],

            // 加载状态
            if (_isLoading) ...[
              const SizedBox(height: 16),
              const Center(
                child: CircularProgressIndicator(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _testCreateProjectDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const CreateProjectDialogWidget(),
    ).then((result) {
      setState(() {
        if (result != null) {
          _lastResult = '对话框创建成功: ${result.toString()}';
        } else {
          _lastResult = '对话框被取消';
        }
      });
    }).catchError((error) {
      setState(() {
        _lastResult = '对话框错误: $error';
      });
    });
  }

  void _testDirectCreate() async {
    setState(() {
      _isLoading = true;
      _lastResult = null;
    });

    try {
      final request = CreateProjectRequest(
        title: '测试项目 ${DateTime.now().millisecondsSinceEpoch}',
        description: '这是一个测试项目，用于验证创建功能',
        vehicleType: '自行式房车',
        vehicleModel: '测试车型',
        vehicleBrand: '测试品牌',
        budget: 50000.0,
        isPublic: true,
        tags: ['电路系统', '水路系统'],
      );

      final result = await ref.read(projectControllerProvider.notifier).createProject(request);

      result.fold(
        (failure) {
          setState(() {
            _lastResult = '直接创建失败: ${failure.message}';
          });
        },
        (project) {
          setState(() {
            _lastResult = '直接创建成功: ${project.title} (ID: ${project.id})';
          });
        },
      );
    } catch (e) {
      setState(() {
        _lastResult = '直接创建异常: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _testTagsField() async {
    setState(() {
      _isLoading = true;
      _lastResult = null;
    });

    try {
      // 测试1: 空tags数组
      final request1 = CreateProjectRequest(
        title: '测试Tags-空数组 ${DateTime.now().millisecondsSinceEpoch}',
        description: '测试空tags数组的项目创建',
        vehicleType: '自行式房车',
        vehicleModel: '测试车型',
        budget: 30000.0,
        isPublic: true,
        tags: [], // 空数组
      );

      final result1 = await ref.read(projectControllerProvider.notifier).createProject(request1);

      String testResult = '🔧 Tags字段修复测试结果:\n\n';

      result1.fold(
        (failure) {
          testResult += '❌ 测试1(空tags)失败: ${failure.message}\n\n';
        },
        (project) {
          testResult += '✅ 测试1(空tags)成功: ${project.title}\n';
          testResult += '   Tags: ${project.tags?.join(", ") ?? "无"}\n\n';
        },
      );

      // 测试2: 多个tags
      final request2 = CreateProjectRequest(
        title: '测试Tags-多标签 ${DateTime.now().millisecondsSinceEpoch}',
        description: '测试多个tags的项目创建',
        vehicleType: '自行式房车',
        vehicleModel: '测试车型',
        budget: 50000.0,
        isPublic: true,
        tags: ['电路系统', '水路系统', '储物系统', '床铺系统', '厨房系统'],
      );

      final result2 = await ref.read(projectControllerProvider.notifier).createProject(request2);

      result2.fold(
        (failure) {
          testResult += '❌ 测试2(多tags)失败: ${failure.message}\n\n';
        },
        (project) {
          testResult += '✅ 测试2(多tags)成功: ${project.title}\n';
          testResult += '   Tags数量: ${project.tags?.length ?? 0}\n';
          testResult += '   Tags内容: ${project.tags?.join(", ") ?? "无"}\n\n';
        },
      );

      testResult += '🎯 如果两个测试都成功，说明数据库tags列修复完成！';

      setState(() {
        _lastResult = testResult;
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ Tags字段测试异常: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
