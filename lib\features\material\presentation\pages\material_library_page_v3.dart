import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/design_system/vanhub_design_system.dart';
import '../../../../core/design_system/foundation/colors/colors.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';
import '../../../../core/design_system/utils/responsive_utils.dart';
import '../../../../core/widgets/loading_widget.dart';
import '../../../../core/widgets/error_display_widget.dart';
import '../../../../core/error/ui_failure.dart';
import '../../../../core/widgets/empty_state_widget.dart';
import '../../domain/entities/material.dart' as domain;
import '../providers/material_provider.dart';
import '../../../../core/providers/auth_state_provider.dart';
import '../widgets/material_card_v2.dart';
import '../widgets/material_search_bar.dart';
import '../widgets/material_filter_panel.dart';

/// 现代化物料库页面 V3
/// 采用VanHub设计系统，支持响应式布局和现代化交互
class MaterialLibraryPageV3 extends ConsumerStatefulWidget {
  final String? userId;

  const MaterialLibraryPageV3({
    super.key,
    this.userId,
  });

  @override
  ConsumerState<MaterialLibraryPageV3> createState() => _MaterialLibraryPageV3State();
}

class _MaterialLibraryPageV3State extends ConsumerState<MaterialLibraryPageV3>
    with TickerProviderStateMixin {
  
  // 搜索和筛选状态
  String _searchQuery = '';
  String _selectedCategory = '全部';
  MaterialViewType _viewType = MaterialViewType.grid;
  MaterialSortType _sortType = MaterialSortType.newest;
  
  // 动画控制器
  late AnimationController _filterAnimationController;
  late AnimationController _viewSwitchController;
  
  // 筛选面板状态
  bool _showFilters = false;
  
  @override
  void initState() {
    super.initState();
    _filterAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _viewSwitchController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _filterAnimationController.dispose();
    _viewSwitchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentUserId = ref.watch(currentUserIdProvider);
    final userId = widget.userId ?? currentUserId;
    final isGuest = userId == null;

    return Scaffold(
      backgroundColor: VanHubColors.surface,
      body: CustomScrollView(
        slivers: [
          _buildModernAppBar(context, isGuest),
          _buildSearchSection(context),
          if (_showFilters) _buildFilterSection(context),
          _buildMaterialContent(context, userId),
        ],
      ),
      floatingActionButton: !isGuest ? _buildCreateFAB(context) : null,
    );
  }

  /// 构建现代化应用栏
  Widget _buildModernAppBar(BuildContext context, bool isGuest) {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: VanHubColors.primary,
      foregroundColor: VanHubColors.onPrimary,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          '材料库',
          style: TextStyle(
            color: VanHubColors.onPrimary,
            fontWeight: FontWeight.bold,
            fontSize: VanHubResponsiveUtils.getValue(
              context,
              xs: 20.0,
              sm: 20.0,
              md: 22.0,
              lg: 24.0,
              xl: 24.0,
              defaultValue: 20.0,
            ),
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                VanHubColors.primary,
                VanHubColors.primary.withOpacity(0.8),
              ],
            ),
          ),
          child: Stack(
            children: [
              Positioned(
                right: -50,
                top: -30,
                child: Icon(
                  Icons.inventory_2_outlined,
                  size: 150,
                  color: VanHubColors.onPrimary.withOpacity(0.1),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: Icon(
            _viewType == MaterialViewType.grid 
                ? Icons.view_list_outlined 
                : Icons.grid_view_outlined,
            color: VanHubColors.onPrimary,
          ),
          onPressed: _toggleViewType,
          tooltip: _viewType == MaterialViewType.grid ? '列表视图' : '网格视图',
        ),
        IconButton(
          icon: Icon(
            Icons.sort_outlined,
            color: VanHubColors.onPrimary,
          ),
          onPressed: _showSortOptions,
          tooltip: '排序选项',
        ),
        const SizedBox(width: VanHubSpacing.sm),
      ],
    );
  }

  /// 构建搜索区域
  Widget _buildSearchSection(BuildContext context) {
    return SliverToBoxAdapter(
      child: Container(
        padding: EdgeInsets.all(VanHubSpacing.lg),
        child: Column(
          children: [
            MaterialSearchBar(
              searchQuery: _searchQuery,
              onSearchChanged: (query) {
                setState(() {
                  _searchQuery = query;
                });
              },
              onFilterToggle: _toggleFilters,
              showFilters: _showFilters,
            ),
            const SizedBox(height: VanHubSpacing.md),
            _buildQuickFilters(context),
          ],
        ),
      ),
    );
  }

  /// 构建快速筛选器
  Widget _buildQuickFilters(BuildContext context) {
    final categories = ['全部', '电路系统', '水路系统', '床铺系统', '储物系统', '厨房系统'];
    
    return SizedBox(
      height: 40,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = _selectedCategory == category;
          
          return Container(
            margin: EdgeInsets.only(right: VanHubSpacing.sm),
            child: FilterChip(
              label: Text(category),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedCategory = selected ? category : '全部';
                });
              },
              backgroundColor: VanHubColors.surface,
              selectedColor: VanHubColors.primaryContainer,
              labelStyle: TextStyle(
                color: isSelected ? VanHubColors.onPrimaryContainer : VanHubColors.onSurface,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
              side: BorderSide(
                color: isSelected ? VanHubColors.primary : VanHubColors.outline,
                width: isSelected ? 2 : 1,
              ),
            ),
          );
        },
      ),
    );
  }

  /// 构建筛选区域
  Widget _buildFilterSection(BuildContext context) {
    return SliverToBoxAdapter(
      child: AnimatedBuilder(
        animation: _filterAnimationController,
        builder: (context, child) {
          return SizeTransition(
            sizeFactor: _filterAnimationController,
            child: MaterialFilterPanel(
              selectedCategory: _selectedCategory,
              onCategoryChanged: (category) {
                setState(() {
                  _selectedCategory = category;
                });
              },
              onReset: () {
                setState(() {
                  _searchQuery = '';
                  _selectedCategory = '全部';
                });
              },
            ),
          );
        },
      ),
    );
  }

  /// 构建物料内容
  Widget _buildMaterialContent(BuildContext context, String? userId) {
    final materialsAsync = userId != null
        ? ref.watch(userMaterialsProvider(userId))
        : ref.watch(materialsNotifierProvider);

    return materialsAsync.when(
      data: (materials) {
        final filteredMaterials = _filterMaterials(materials);
        
        if (filteredMaterials.isEmpty) {
          return SliverToBoxAdapter(
            child: _buildEmptyState(context, userId != null),
          );
        }
        
        return _buildMaterialLayout(context, filteredMaterials);
      },
      loading: () => SliverToBoxAdapter(
        child: _buildLoadingState(context),
      ),
      error: (error, stack) => SliverToBoxAdapter(
        child: _buildErrorState(context, error),
      ),
    );
  }

  /// 过滤物料
  List<domain.Material> _filterMaterials(List<domain.Material> materials) {
    var filtered = materials.where((material) {
      // 搜索筛选
      final matchesSearch = _searchQuery.trim().isEmpty ||
          material.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (material.brand?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) ||
          (material.specifications?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);
      
      // 分类筛选
      final matchesCategory = _selectedCategory == '全部' ||
          material.category == _selectedCategory;
      
      return matchesSearch && matchesCategory;
    }).toList();
    
    // 排序
    switch (_sortType) {
      case MaterialSortType.newest:
        filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case MaterialSortType.oldest:
        filtered.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case MaterialSortType.priceHigh:
        filtered.sort((a, b) => b.price.compareTo(a.price));
        break;
      case MaterialSortType.priceLow:
        filtered.sort((a, b) => a.price.compareTo(b.price));
        break;
      case MaterialSortType.name:
        filtered.sort((a, b) => a.name.compareTo(b.name));
        break;
    }
    
    return filtered;
  }

  /// 构建物料布局
  Widget _buildMaterialLayout(BuildContext context, List<domain.Material> materials) {
    switch (_viewType) {
      case MaterialViewType.grid:
        return _buildGridLayout(context, materials);
      case MaterialViewType.list:
        return _buildListLayout(context, materials);
      case MaterialViewType.compact:
        return _buildCompactLayout(context, materials);
    }
  }

  /// 构建网格布局
  Widget _buildGridLayout(BuildContext context, List<domain.Material> materials) {
    final crossAxisCount = VanHubResponsiveUtils.getValue(
      context,
      xs: 2,
      sm: 2,
      md: 3,
      lg: 4,
      xl: 4,
      defaultValue: 2,
    );

    return SliverPadding(
      padding: EdgeInsets.all(VanHubSpacing.lg),
      sliver: SliverGrid(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          childAspectRatio: 0.75,
          crossAxisSpacing: VanHubSpacing.md,
          mainAxisSpacing: VanHubSpacing.md,
        ),
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            return MaterialCardV2(
              material: materials[index],
              viewType: MaterialCardViewType.grid,
              onTap: () => _showMaterialDetails(materials[index]),
              onEdit: () => _editMaterial(materials[index]),
              onDelete: () => _deleteMaterial(materials[index]),
              onAddToBom: () => _addToBom(materials[index]),
            );
          },
          childCount: materials.length,
        ),
      ),
    );
  }

  /// 构建列表布局
  Widget _buildListLayout(BuildContext context, List<domain.Material> materials) {
    return SliverPadding(
      padding: EdgeInsets.all(VanHubSpacing.lg),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            return Container(
              margin: EdgeInsets.only(bottom: VanHubSpacing.md),
              child: MaterialCardV2(
                material: materials[index],
                viewType: MaterialCardViewType.list,
                onTap: () => _showMaterialDetails(materials[index]),
                onEdit: () => _editMaterial(materials[index]),
                onDelete: () => _deleteMaterial(materials[index]),
                onAddToBom: () => _addToBom(materials[index]),
              ),
            );
          },
          childCount: materials.length,
        ),
      ),
    );
  }

  /// 构建紧凑布局
  Widget _buildCompactLayout(BuildContext context, List<domain.Material> materials) {
    return SliverPadding(
      padding: EdgeInsets.all(VanHubSpacing.lg),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            return Container(
              margin: EdgeInsets.only(bottom: VanHubSpacing.sm),
              child: MaterialCardV2(
                material: materials[index],
                viewType: MaterialCardViewType.compact,
                onTap: () => _showMaterialDetails(materials[index]),
                onEdit: () => _editMaterial(materials[index]),
                onDelete: () => _deleteMaterial(materials[index]),
                onAddToBom: () => _addToBom(materials[index]),
              ),
            );
          },
          childCount: materials.length,
        ),
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState(BuildContext context, bool isLoggedIn) {
    return Container(
      height: 400,
      padding: EdgeInsets.all(VanHubSpacing.xl),
      child: EmptyStateWidget(
        icon: Icons.inventory_2_outlined,
        title: _searchQuery.isNotEmpty ? '未找到匹配的材料' : '暂无材料',
        subtitle: _searchQuery.isNotEmpty
            ? '尝试调整搜索条件或分类筛选'
            : isLoggedIn
                ? '点击右下角按钮添加您的第一个材料'
                : '当前没有公开的材料数据，请登录后查看您的材料库',
        actionText: _searchQuery.isNotEmpty
            ? '清除搜索'
            : isLoggedIn
                ? '添加材料'
                : '立即登录',
        onAction: _searchQuery.isNotEmpty
            ? () {
                setState(() {
                  _searchQuery = '';
                  _selectedCategory = '全部';
                });
              }
            : isLoggedIn
                ? () => _showCreateMaterialDialog(context)
                : () => _showLoginPrompt(context),
      ),
    );
  }

  /// 构建加载状态
  Widget _buildLoadingState(BuildContext context) {
    return Container(
      height: 400,
      child: const LoadingWidget(message: '正在加载材料库...'),
    );
  }

  /// 构建错误状态
  Widget _buildErrorState(BuildContext context, Object error) {
    return Container(
      height: 400,
      padding: EdgeInsets.all(VanHubSpacing.xl),
      child: ErrorDisplayWidget(
        failure: UIFailure.network(
          message: '加载失败: ${error.toString()}',
        ),
        onRetry: () {
          ref.invalidate(materialsNotifierProvider);
        },
      ),
    );
  }

  /// 构建创建材料FAB
  Widget _buildCreateFAB(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: () => _showCreateMaterialDialog(context),
      backgroundColor: VanHubColors.primary,
      foregroundColor: VanHubColors.onPrimary,
      icon: const Icon(Icons.add),
      label: const Text('添加材料'),
      elevation: 4,
      highlightElevation: 8,
    );
  }

  // ============================================================================
  // 交互方法 (Interaction Methods)
  // ============================================================================

  /// 切换视图类型
  void _toggleViewType() {
    setState(() {
      switch (_viewType) {
        case MaterialViewType.grid:
          _viewType = MaterialViewType.list;
          break;
        case MaterialViewType.list:
          _viewType = MaterialViewType.compact;
          break;
        case MaterialViewType.compact:
          _viewType = MaterialViewType.grid;
          break;
      }
    });
    _viewSwitchController.forward().then((_) {
      _viewSwitchController.reset();
    });
  }

  /// 切换筛选面板
  void _toggleFilters() {
    setState(() {
      _showFilters = !_showFilters;
    });
    if (_showFilters) {
      _filterAnimationController.forward();
    } else {
      _filterAnimationController.reverse();
    }
  }

  /// 显示排序选项
  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: VanHubColors.surface,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: EdgeInsets.all(VanHubSpacing.lg),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '排序方式',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: VanHubColors.onSurface,
                ),
              ),
              const SizedBox(height: VanHubSpacing.lg),
              ...MaterialSortType.values.map((sortType) {
                return ListTile(
                  leading: Icon(
                    _getSortIcon(sortType),
                    color: _sortType == sortType ? VanHubColors.primary : VanHubColors.onSurface,
                  ),
                  title: Text(
                    _getSortLabel(sortType),
                    style: TextStyle(
                      color: _sortType == sortType ? VanHubColors.primary : VanHubColors.onSurface,
                      fontWeight: _sortType == sortType ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                  trailing: _sortType == sortType
                      ? Icon(Icons.check, color: VanHubColors.primary)
                      : null,
                  onTap: () {
                    setState(() {
                      _sortType = sortType;
                    });
                    Navigator.pop(context);
                  },
                );
              }).toList(),
              const SizedBox(height: VanHubSpacing.md),
            ],
          ),
        );
      },
    );
  }

  /// 显示材料详情
  void _showMaterialDetails(domain.Material material) {
    // TODO: 导航到材料详情页面
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('查看材料详情: ${material.name}')),
    );
  }

  /// 编辑材料
  void _editMaterial(domain.Material material) {
    // TODO: 显示编辑材料对话框
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('编辑材料: ${material.name}')),
    );
  }

  /// 删除材料
  void _deleteMaterial(domain.Material material) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除材料"${material.name}"吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: 实现删除逻辑
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('已删除材料: ${material.name}')),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: VanHubColors.error,
              foregroundColor: VanHubColors.onError,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  /// 添加到BOM
  void _addToBom(domain.Material material) {
    // TODO: 显示添加到BOM对话框
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('添加到BOM: ${material.name}')),
    );
  }

  /// 显示创建材料对话框
  void _showCreateMaterialDialog(BuildContext context) {
    // TODO: 显示创建材料对话框
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('创建新材料')),
    );
  }

  /// 显示登录提示
  void _showLoginPrompt(BuildContext context) {
    // TODO: 显示登录对话框或导航到登录页面
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('请先登录')),
    );
  }

  // ============================================================================
  // 辅助方法 (Helper Methods)
  // ============================================================================

  /// 获取排序图标
  IconData _getSortIcon(MaterialSortType sortType) {
    switch (sortType) {
      case MaterialSortType.newest:
        return Icons.schedule;
      case MaterialSortType.oldest:
        return Icons.history;
      case MaterialSortType.priceHigh:
        return Icons.trending_up;
      case MaterialSortType.priceLow:
        return Icons.trending_down;
      case MaterialSortType.name:
        return Icons.sort_by_alpha;
    }
  }

  /// 获取排序标签
  String _getSortLabel(MaterialSortType sortType) {
    switch (sortType) {
      case MaterialSortType.newest:
        return '最新添加';
      case MaterialSortType.oldest:
        return '最早添加';
      case MaterialSortType.priceHigh:
        return '价格从高到低';
      case MaterialSortType.priceLow:
        return '价格从低到高';
      case MaterialSortType.name:
        return '按名称排序';
    }
  }
}

// ============================================================================
// 枚举定义 (Enums)
// ============================================================================

/// 物料视图类型
enum MaterialViewType {
  /// 网格视图
  grid,
  /// 列表视图
  list,
  /// 紧凑视图
  compact,
}

/// 物料排序类型
enum MaterialSortType {
  /// 最新添加
  newest,
  /// 最早添加
  oldest,
  /// 价格从高到低
  priceHigh,
  /// 价格从低到高
  priceLow,
  /// 按名称排序
  name,
}

/// 物料卡片视图类型
enum MaterialCardViewType {
  /// 网格卡片
  grid,
  /// 列表卡片
  list,
  /// 紧凑卡片
  compact,
}
