import 'package:flutter/material.dart';
import '../../../../core/design_system/foundation/colors/colors.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';

/// 物料搜索栏组件
/// 提供现代化的搜索体验和筛选功能
class MaterialSearchBar extends StatefulWidget {
  final String searchQuery;
  final ValueChanged<String> onSearchChanged;
  final VoidCallback onFilterToggle;
  final bool showFilters;

  const MaterialSearchBar({
    super.key,
    required this.searchQuery,
    required this.onSearchChanged,
    required this.onFilterToggle,
    required this.showFilters,
  });

  @override
  State<MaterialSearchBar> createState() => _MaterialSearchBarState();
}

class _MaterialSearchBarState extends State<MaterialSearchBar>
    with SingleTickerProviderStateMixin {
  
  late TextEditingController _controller;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.searchQuery);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(MaterialSearchBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.searchQuery != oldWidget.searchQuery) {
      _controller.text = widget.searchQuery;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: VanHubColors.shadow.withOpacity(_isFocused ? 0.15 : 0.08),
                  blurRadius: _isFocused ? 12 : 6,
                  offset: Offset(0, _isFocused ? 4 : 2),
                ),
              ],
            ),
            child: TextField(
              controller: _controller,
              onChanged: widget.onSearchChanged,
              onTap: _handleFocus,
              onTapOutside: (_) => _handleUnfocus(),
              decoration: InputDecoration(
                hintText: '搜索材料名称、品牌或规格...',
                hintStyle: TextStyle(
                  color: VanHubColors.onSurface.withOpacity(0.5),
                  fontSize: 16,
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: _isFocused 
                      ? VanHubColors.primary 
                      : VanHubColors.onSurface.withOpacity(0.5),
                  size: 24,
                ),
                suffixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (_controller.text.isNotEmpty)
                      IconButton(
                        icon: Icon(
                          Icons.clear,
                          color: VanHubColors.onSurface.withOpacity(0.5),
                          size: 20,
                        ),
                        onPressed: _clearSearch,
                        tooltip: '清除搜索',
                      ),
                    Container(
                      width: 1,
                      height: 24,
                      color: VanHubColors.outline.withOpacity(0.3),
                      margin: EdgeInsets.symmetric(horizontal: VanHubSpacing.xs),
                    ),
                    IconButton(
                      icon: AnimatedRotation(
                        turns: widget.showFilters ? 0.5 : 0,
                        duration: const Duration(milliseconds: 200),
                        child: Icon(
                          Icons.tune,
                          color: widget.showFilters 
                              ? VanHubColors.primary 
                              : VanHubColors.onSurface.withOpacity(0.7),
                          size: 20,
                        ),
                      ),
                      onPressed: widget.onFilterToggle,
                      tooltip: widget.showFilters ? '隐藏筛选' : '显示筛选',
                    ),
                  ],
                ),
                filled: true,
                fillColor: VanHubColors.surface,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: VanHubColors.outline.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: VanHubColors.outline.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: VanHubColors.primary,
                    width: 2,
                  ),
                ),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: VanHubSpacing.lg,
                  vertical: VanHubSpacing.md,
                ),
              ),
              style: TextStyle(
                color: VanHubColors.onSurface,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        );
      },
    );
  }

  /// 处理获得焦点
  void _handleFocus() {
    setState(() {
      _isFocused = true;
    });
    _animationController.forward();
  }

  /// 处理失去焦点
  void _handleUnfocus() {
    setState(() {
      _isFocused = false;
    });
    _animationController.reverse();
  }

  /// 清除搜索
  void _clearSearch() {
    _controller.clear();
    widget.onSearchChanged('');
  }
}

/// 搜索建议组件
class MaterialSearchSuggestions extends StatelessWidget {
  final List<String> suggestions;
  final ValueChanged<String> onSuggestionTap;

  const MaterialSearchSuggestions({
    super.key,
    required this.suggestions,
    required this.onSuggestionTap,
  });

  @override
  Widget build(BuildContext context) {
    if (suggestions.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: EdgeInsets.only(top: VanHubSpacing.sm),
      decoration: BoxDecoration(
        color: VanHubColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: VanHubColors.shadow.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: VanHubColors.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(VanHubSpacing.md),
            child: Text(
              '搜索建议',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: VanHubColors.onSurface.withOpacity(0.7),
              ),
            ),
          ),
          ...suggestions.map((suggestion) => _buildSuggestionItem(suggestion)),
        ],
      ),
    );
  }

  Widget _buildSuggestionItem(String suggestion) {
    return InkWell(
      onTap: () => onSuggestionTap(suggestion),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(
          horizontal: VanHubSpacing.md,
          vertical: VanHubSpacing.sm,
        ),
        child: Row(
          children: [
            Icon(
              Icons.search,
              size: 16,
              color: VanHubColors.onSurface.withOpacity(0.5),
            ),
            const SizedBox(width: VanHubSpacing.sm),
            Expanded(
              child: Text(
                suggestion,
                style: TextStyle(
                  fontSize: 14,
                  color: VanHubColors.onSurface,
                ),
              ),
            ),
            Icon(
              Icons.north_west,
              size: 14,
              color: VanHubColors.onSurface.withOpacity(0.3),
            ),
          ],
        ),
      ),
    );
  }
}

/// 搜索历史组件
class MaterialSearchHistory extends StatelessWidget {
  final List<String> history;
  final ValueChanged<String> onHistoryTap;
  final VoidCallback onClearHistory;

  const MaterialSearchHistory({
    super.key,
    required this.history,
    required this.onHistoryTap,
    required this.onClearHistory,
  });

  @override
  Widget build(BuildContext context) {
    if (history.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: EdgeInsets.only(top: VanHubSpacing.sm),
      decoration: BoxDecoration(
        color: VanHubColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: VanHubColors.shadow.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: VanHubColors.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(VanHubSpacing.md),
            child: Row(
              children: [
                Text(
                  '搜索历史',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: VanHubColors.onSurface.withOpacity(0.7),
                  ),
                ),
                const Spacer(),
                InkWell(
                  onTap: onClearHistory,
                  borderRadius: BorderRadius.circular(4),
                  child: Padding(
                    padding: EdgeInsets.all(VanHubSpacing.xs),
                    child: Text(
                      '清除',
                      style: TextStyle(
                        fontSize: 12,
                        color: VanHubColors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          ...history.take(5).map((item) => _buildHistoryItem(item)),
        ],
      ),
    );
  }

  Widget _buildHistoryItem(String item) {
    return InkWell(
      onTap: () => onHistoryTap(item),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(
          horizontal: VanHubSpacing.md,
          vertical: VanHubSpacing.sm,
        ),
        child: Row(
          children: [
            Icon(
              Icons.history,
              size: 16,
              color: VanHubColors.onSurface.withOpacity(0.5),
            ),
            const SizedBox(width: VanHubSpacing.sm),
            Expanded(
              child: Text(
                item,
                style: TextStyle(
                  fontSize: 14,
                  color: VanHubColors.onSurface,
                ),
              ),
            ),
            Icon(
              Icons.north_west,
              size: 14,
              color: VanHubColors.onSurface.withOpacity(0.3),
            ),
          ],
        ),
      ),
    );
  }
}
