-- VanHub数据库预算字段精度修复脚本
-- 执行日期：2025-01-27
-- 目的：修复total_budget字段精度限制问题

-- ========================================
-- 问题说明
-- ========================================
-- 当前：total_budget DECIMAL(10,2) - 最大值 99,999,999.99 (约1亿)
-- 需要：支持更大的预算金额，比如千万级别的改装项目

-- ========================================
-- 第一部分：检查当前字段定义
-- ========================================

SELECT 
    '=== 当前total_budget字段定义 ===' as info,
    column_name,
    data_type,
    numeric_precision,
    numeric_scale,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'projects' 
AND column_name = 'total_budget'
AND table_schema = 'public';

-- ========================================
-- 第二部分：修改字段精度
-- ========================================

-- 将total_budget字段从DECIMAL(10,2)改为DECIMAL(15,2)
-- 新的最大值：999,999,999,999.99 (约1万亿)
ALTER TABLE public.projects 
ALTER COLUMN total_budget TYPE DECIMAL(15,2);

-- ========================================
-- 第三部分：验证修改结果
-- ========================================

SELECT 
    '=== 修改后的total_budget字段定义 ===' as info,
    column_name,
    data_type,
    numeric_precision,
    numeric_scale,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'projects' 
AND column_name = 'total_budget'
AND table_schema = 'public';

-- ========================================
-- 第四部分：测试新的精度限制
-- ========================================

-- 显示新的字段限制信息
SELECT 
    '=== 新的预算字段限制 ===' as info,
    'DECIMAL(15,2)' as new_type,
    '999,999,999,999.99' as max_value,
    '约1万亿人民币' as max_value_description,
    '完全满足房车改装预算需求' as note;

-- 提示：现在可以支持的预算范围
-- 最小值：-999,999,999,999.99
-- 最大值：+999,999,999,999.99
-- 精度：小数点后2位（分）
