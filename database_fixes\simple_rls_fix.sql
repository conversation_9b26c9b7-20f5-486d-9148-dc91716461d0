-- VanHub数据库RLS策略简化修复脚本
-- 执行日期：2025-01-27
-- 目的：快速修复项目数据读取权限问题

-- ========================================
-- 第一部分：清理现有策略
-- ========================================

-- 删除可能存在的有问题的策略
DROP POLICY IF EXISTS "Enable read access for all users" ON public.projects;
DROP POLICY IF EXISTS "Enable read access for authenticated users only" ON public.projects;
DROP POLICY IF EXISTS "Enable read access for public projects" ON public.projects;
DROP POLICY IF EXISTS "Users can view public projects" ON public.projects;
DROP POLICY IF EXISTS "Public projects are viewable by everyone" ON public.projects;
DROP POLICY IF EXISTS "Allow public read access to public projects" ON public.projects;
DROP POLICY IF EXISTS "Users can read own projects" ON public.projects;

-- ========================================
-- 第二部分：创建新的RLS策略
-- ========================================

-- 策略1：允许所有人（包括游客）读取公开项目
CREATE POLICY "public_projects_read" ON public.projects
    FOR SELECT
    USING (is_public = true);

-- 策略2：用户可以读取自己的所有项目
CREATE POLICY "own_projects_read" ON public.projects
    FOR SELECT
    USING (auth.uid() = user_id);

-- 策略3：认证用户可以创建项目
CREATE POLICY "authenticated_insert" ON public.projects
    FOR INSERT
    WITH CHECK (auth.uid() = user_id);

-- 策略4：用户可以更新自己的项目
CREATE POLICY "own_projects_update" ON public.projects
    FOR UPDATE
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- 策略5：用户可以删除自己的项目
CREATE POLICY "own_projects_delete" ON public.projects
    FOR DELETE
    USING (auth.uid() = user_id);

-- ========================================
-- 第三部分：确保RLS已启用
-- ========================================

-- 启用RLS
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;

-- ========================================
-- 第四部分：验证修复结果
-- ========================================

-- 查看当前策略
SELECT 
    policyname,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'projects' AND schemaname = 'public'
ORDER BY policyname;

-- 测试查询公开项目
SELECT 
    COUNT(*) as public_projects_count
FROM public.projects 
WHERE is_public = true;

-- 显示成功消息
SELECT '✅ RLS策略修复完成！游客现在可以查看公开项目了。' as result;
