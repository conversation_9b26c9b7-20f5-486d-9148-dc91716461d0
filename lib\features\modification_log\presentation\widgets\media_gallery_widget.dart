import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/log_media.dart';
import '../../domain/entities/enums.dart';

/// 媒体画廊组件
class MediaGalleryWidget extends ConsumerWidget {
  final List<LogMedia> mediaList;
  final Function(LogMedia) onMediaSelected;
  final bool showTitle;

  const MediaGalleryWidget({
    Key? key,
    required this.mediaList,
    required this.onMediaSelected,
    this.showTitle = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (mediaList.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) ...[
          Text(
            '媒体文件 (${mediaList.length})',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
        ],
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: mediaList.length,
            itemBuilder: (context, index) {
              final media = mediaList[index];
              return GestureDetector(
                onTap: () => onMediaSelected(media),
                child: Container(
                  margin: const EdgeInsets.only(right: 8),
                  width: 120,
                  height: 120,
                  child: _buildMediaPreview(media),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// 构建媒体预览
  Widget _buildMediaPreview(LogMedia media) {
    if (media.isImage) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Stack(
          fit: StackFit.expand,
          children: [
            Image.network(
              media.previewUrl,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[300],
                  child: const Icon(Icons.broken_image),
                );
              },
            ),
            if (media.caption != null && media.caption!.isNotEmpty)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                  color: Colors.black.withValues(alpha: 0.5),
                  child: Text(
                    media.caption!,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
          ],
        ),
      );
    } else {
      return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Container(
          color: Colors.grey[200],
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                media.isVideo ? Icons.video_library :
                media.isDocument ? Icons.description :
                media.isCad ? Icons.architecture :
                media.isPanorama ? Icons.panorama :
                media.isAudio ? Icons.audiotrack :
                Icons.insert_drive_file,
                size: 48,
                color: Colors.grey[600],
              ),
              const SizedBox(height: 8),
              Text(
                _getMediaTypeText(media.type),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  /// 获取媒体类型文本
  String _getMediaTypeText(MediaType type) {
    switch (type) {
      case MediaType.image:
        return '图片';
      case MediaType.video:
        return '视频';
      case MediaType.document:
        return '文档';
      case MediaType.cad:
        return 'CAD';
      case MediaType.panorama:
        return '全景图';
      case MediaType.audio:
        return '音频';
      case MediaType.other:
        return '其他';
    }
  }
}