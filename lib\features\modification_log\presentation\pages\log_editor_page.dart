import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:html_editor_enhanced/html_editor.dart';
import 'package:uuid/uuid.dart';

import '../../../../core/providers/auth_state_provider.dart';
import '../../domain/entities/log_entry.dart';
import '../../domain/entities/enums.dart';
import '../providers/log_provider.dart';

/// 日志编辑页面
class LogEditorPage extends ConsumerStatefulWidget {
  final String projectId;
  final String systemId;
  final LogEntry? logEntry;

  const LogEditorPage({
    Key? key,
    required this.projectId,
    required this.systemId,
    this.logEntry,
  }) : super(key: key);

  @override
  ConsumerState<LogEditorPage> createState() => _LogEditorPageState();
}

class _LogEditorPageState extends ConsumerState<LogEditorPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _htmlEditorController = HtmlEditorController();
  final _timeSpentController = TextEditingController();
  
  DifficultyLevel _selectedDifficulty = DifficultyLevel.medium;
  LogStatus _selectedStatus = LogStatus.completed;
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  /// 初始化表单
  void _initializeForm() {
    if (widget.logEntry != null) {
      _titleController.text = widget.logEntry!.title;
      _timeSpentController.text = widget.logEntry!.timeSpentMinutes.toString();
      _selectedDifficulty = widget.logEntry!.difficulty;
      _selectedStatus = widget.logEntry!.status;
      _selectedDate = widget.logEntry!.logDate;
      
      // 延迟设置HTML内容，确保编辑器已初始化
      Future.delayed(const Duration(milliseconds: 500), () {
        _htmlEditorController.setText(widget.logEntry!.content);
      });
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _timeSpentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.logEntry == null ? '创建日志' : '编辑日志'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveLog,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                : const Text(
                    '保存',
                    style: TextStyle(color: Colors.white),
                  ),
          ),
        ],
      ),
      body: _buildForm(),
    );
  }

  /// 构建表单
  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTitleField(),
                  const SizedBox(height: 16),
                  _buildDatePicker(),
                  const SizedBox(height: 16),
                  _buildDifficultySelector(),
                  const SizedBox(height: 16),
                  _buildTimeSpentField(),
                  const SizedBox(height: 16),
                  _buildStatusSelector(),
                  const SizedBox(height: 24),
                  _buildContentEditor(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建标题输入框
  Widget _buildTitleField() {
    return TextFormField(
      controller: _titleController,
      decoration: const InputDecoration(
        labelText: '标题',
        hintText: '输入日志标题',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.title),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return '请输入标题';
        }
        return null;
      },
    );
  }

  /// 构建日期选择器
  Widget _buildDatePicker() {
    return InkWell(
      onTap: _selectDate,
      child: InputDecorator(
        decoration: const InputDecoration(
          labelText: '日期',
          border: OutlineInputBorder(),
          prefixIcon: Icon(Icons.calendar_today),
        ),
        child: Text(
          '${_selectedDate.year}-${_selectedDate.month.toString().padLeft(2, '0')}-${_selectedDate.day.toString().padLeft(2, '0')}',
        ),
      ),
    );
  }

  /// 构建难度选择器
  Widget _buildDifficultySelector() {
    return InputDecorator(
      decoration: const InputDecoration(
        labelText: '难度级别',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.trending_up),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<DifficultyLevel>(
          value: _selectedDifficulty,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedDifficulty = value;
              });
            }
          },
          items: [
            DifficultyLevel.easy,
            DifficultyLevel.medium,
            DifficultyLevel.hard,
            DifficultyLevel.expert,
          ].map((difficulty) {
            return DropdownMenuItem<DifficultyLevel>(
              value: difficulty,
              child: Text(_getDifficultyText(difficulty)),
            );
          }).toList(),
        ),
      ),
    );
  }

  /// 构建耗时输入框
  Widget _buildTimeSpentField() {
    return TextFormField(
      controller: _timeSpentController,
      decoration: const InputDecoration(
        labelText: '耗时（分钟）',
        hintText: '输入耗时',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.access_time),
      ),
      keyboardType: TextInputType.number,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return '请输入耗时';
        }
        if (int.tryParse(value) == null) {
          return '请输入有效的数字';
        }
        return null;
      },
    );
  }

  /// 构建状态选择器
  Widget _buildStatusSelector() {
    return InputDecorator(
      decoration: const InputDecoration(
        labelText: '状态',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.flag),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<LogStatus>(
          value: _selectedStatus,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedStatus = value;
              });
            }
          },
          items: [
            LogStatus.completed,
            LogStatus.inProgress,
            LogStatus.onHold,
            LogStatus.cancelled,
          ].map((status) {
            return DropdownMenuItem<LogStatus>(
              value: status,
              child: Text(_getStatusText(status)),
            );
          }).toList(),
        ),
      ),
    );
  }

  /// 构建内容编辑器
  Widget _buildContentEditor() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '日志内容',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(4),
          ),
          height: 400,
          child: HtmlEditor(
            controller: _htmlEditorController,
            htmlEditorOptions: const HtmlEditorOptions(
              hint: '输入日志内容...',
              initialText: '',
            ),
            htmlToolbarOptions: const HtmlToolbarOptions(
              defaultToolbarButtons: [
                StyleButtons(),
                FontSettingButtons(),
                FontButtons(),
                ColorButtons(),
                ListButtons(),
                ParagraphButtons(),
                InsertButtons(
                  picture: true,
                  video: false,
                  audio: false,
                  table: true,
                  hr: true,
                ),
              ],
            ),
            otherOptions: const OtherOptions(
              height: 400,
            ),
          ),
        ),
      ],
    );
  }

  /// 选择日期
  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  /// 保存日志
  Future<void> _saveLog() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final content = await _htmlEditorController.getText();
      if (content.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('请输入日志内容')),
        );
        setState(() {
          _isLoading = false;
        });
        return;
      }

      final timeSpent = int.parse(_timeSpentController.text);

      if (widget.logEntry == null) {
        // 获取当前用户ID
        final currentUserId = ref.read(currentUserIdProvider);
        if (currentUserId == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('创建失败: 用户未登录，请先登录')),
          );
          return;
        }

        // 创建新日志
        final newLog = LogEntry(
          id: const Uuid().v4(),
          projectId: widget.projectId,
          systemId: widget.systemId,
          title: _titleController.text,
          content: content,
          logDate: _selectedDate,
          authorId: currentUserId, // 使用真实的用户ID
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          status: _selectedStatus,
          difficulty: _selectedDifficulty,
          timeSpentMinutes: timeSpent,
        );

        final result = await ref.read(logProvider.notifier).createLogEntry(newLog);

        result.fold(
          (failure) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('创建失败: ${failure.message}')),
            );
          },
          (_) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('日志创建成功')),
            );
            Navigator.pop(context, true);
          },
        );
      } else {
        // 更新现有日志
        final updatedLog = widget.logEntry!.copyWith(
          title: _titleController.text,
          content: content,
          logDate: _selectedDate,
          updatedAt: DateTime.now(),
          status: _selectedStatus,
          difficulty: _selectedDifficulty,
          timeSpentMinutes: timeSpent,
        );

        final result = await ref.read(logProvider.notifier).updateLogEntry(updatedLog);

        result.fold(
          (failure) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('更新失败: ${failure.message}')),
            );
          },
          (_) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('日志更新成功')),
            );
            Navigator.pop(context, true);
          },
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 获取难度级别文本
  String _getDifficultyText(DifficultyLevel difficulty) {
    switch (difficulty) {
      case DifficultyLevel.easy:
        return '简单';
      case DifficultyLevel.medium:
        return '中等';
      case DifficultyLevel.hard:
        return '困难';
      case DifficultyLevel.expert:
        return '专家';
      case DifficultyLevel.beginner:
        return '初级';
      case DifficultyLevel.intermediate:
        return '中级';
      case DifficultyLevel.advanced:
        return '高级';
    }
  }

  /// 获取状态文本
  String _getStatusText(LogStatus status) {
    switch (status) {
      case LogStatus.draft:
        return '草稿';
      case LogStatus.inProgress:
        return '进行中';
      case LogStatus.completed:
        return '已完成';
      case LogStatus.onHold:
        return '暂停';
      case LogStatus.cancelled:
        return '已取消';
      case LogStatus.published:
        return '已发布';
      case LogStatus.archived:
        return '已归档';
    }
  }
}