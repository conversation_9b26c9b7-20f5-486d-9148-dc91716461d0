/// VanHub Project Management Page 2.0
/// 
/// 现代化项目管理页面，使用新设计系统
/// 
/// 特性：
/// - 3D项目卡片展示
/// - 智能搜索和筛选
/// - 拖拽排序功能
/// - 实时协作状态
/// - 响应式瀑布流布局

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../../core/design_system/components/vanhub_project_card.dart' as card;
import '../../../../core/design_system/components/vanhub_button_v2.dart';
import '../../../../core/design_system/components/vanhub_input_v2.dart';
import '../../../../core/design_system/components/vanhub_card_v2.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';
import '../../../../core/design_system/foundation/colors/semantic_colors.dart';
import '../../../../core/design_system/foundation/spacing/responsive_spacing.dart';
import '../../../../core/design_system/foundation/animations/animation_tokens.dart';
import '../../../../core/design_system/utils/responsive_utils.dart';

import '../providers/project_provider.dart';
import '../widgets/create_project_dialog_widget.dart';

/// VanHub项目管理页面 2.0
class ProjectManagementPageV2 extends ConsumerStatefulWidget {
  const ProjectManagementPageV2({Key? key}) : super(key: key);

  @override
  ConsumerState<ProjectManagementPageV2> createState() => _ProjectManagementPageV2State();
}

class _ProjectManagementPageV2State extends ConsumerState<ProjectManagementPageV2>
    with TickerProviderStateMixin {
  late TextEditingController _searchController;
  late AnimationController _filterController;
  
  String _searchQuery = '';
  String _selectedCategory = '全部';
  ProjectSortType _sortType = ProjectSortType.newest;
  bool _showFilters = false;
  
  final List<String> _categories = [
    '全部', '电力系统', '水路系统', '内饰改装', '外观改装', 
    '储物方案', '床铺设计', '厨房改装', '卫浴改装', '其他'
  ];

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _filterController = AnimationController(
      duration: VanHubAnimationDurations.normal,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _filterController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: VanHubSemanticColors.getBackgroundColor(context),
      body: CustomScrollView(
        slivers: [
          _buildModernAppBar(),
          _buildSearchAndFilters(),
          _buildProjectGrid(),
        ],
      ),
      floatingActionButton: _buildCreateProjectFAB(),
    );
  }

  /// 构建现代化AppBar
  Widget _buildModernAppBar() {
    return SliverAppBar(
      expandedHeight: 200,
      floating: false,
      pinned: true,
      backgroundColor: VanHubBrandColors.primary,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'VanHub 项目管理',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: VanHubBrandColors.onPrimary,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: VanHubBrandColors.getEmotionalGradient(EmotionalState.confident),
          ),
          child: Stack(
            children: [
              // 背景装饰
              Positioned(
                right: -50,
                top: 50,
                child: Icon(
                  Icons.folder_outlined,
                  size: 150,
                  color: VanHubBrandColors.onPrimary.withOpacity(0.1),
                ),
              ),
              // 统计信息
              Positioned(
                left: VanHubResponsiveSpacing.lg,
                bottom: VanHubResponsiveSpacing.xl,
                child: _buildProjectStats(),
              ),
            ],
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () {
            // TODO: 实现全局搜索
          },
        ),
        IconButton(
          icon: const Icon(Icons.more_vert),
          onPressed: () {
            // TODO: 显示更多选项
          },
        ),
      ],
    );
  }

  /// 构建项目统计
  Widget _buildProjectStats() {
    return Row(
      children: [
        _buildStatItem('总项目', '12', VanHubBrandColors.onPrimary),
        SizedBox(width: VanHubResponsiveSpacing.lg),
        _buildStatItem('进行中', '5', VanHubSemanticColors.warning),
        SizedBox(width: VanHubResponsiveSpacing.lg),
        _buildStatItem('已完成', '7', VanHubSemanticColors.success),
      ],
    );
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: VanHubBrandColors.onPrimary.withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  /// 构建搜索和筛选
  Widget _buildSearchAndFilters() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: EdgeInsets.all(VanHubResponsiveSpacing.lg),
        child: Column(
          children: [
            // 搜索栏
            VanHubInputV2(
              controller: _searchController,
              hint: '搜索项目名称、描述或标签...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: IconButton(
                icon: Icon(
                  _showFilters ? Icons.filter_list : Icons.filter_list_outlined,
                  color: _showFilters ? VanHubBrandColors.primary : null,
                ),
                onPressed: _toggleFilters,
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            
            // 筛选器
            AnimatedBuilder(
              animation: _filterController,
              builder: (context, child) {
                return SizeTransition(
                  sizeFactor: _filterController,
                  child: _buildFilterSection(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 构建筛选区域
  Widget _buildFilterSection() {
    return Padding(
      padding: EdgeInsets.only(top: VanHubResponsiveSpacing.md),
      child: VanHubCardV2.outlined(
        size: VanHubCardSize.sm,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '筛选条件',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: VanHubResponsiveSpacing.md),
            
            // 分类筛选
            Text(
              '项目分类',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: VanHubResponsiveSpacing.sm),
            Wrap(
              spacing: VanHubResponsiveSpacing.sm,
              runSpacing: VanHubResponsiveSpacing.sm,
              children: _categories.map((category) {
                final isSelected = category == _selectedCategory;
                return FilterChip(
                  label: Text(category),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      _selectedCategory = category;
                    });
                  },
                  backgroundColor: VanHubSemanticColors.getBackgroundColor(context, level: 2),
                  selectedColor: VanHubBrandColors.primaryContainer,
                  labelStyle: TextStyle(
                    color: isSelected 
                        ? VanHubBrandColors.onPrimaryContainer
                        : VanHubSemanticColors.getTextColor(context),
                  ),
                );
              }).toList(),
            ),
            
            SizedBox(height: VanHubResponsiveSpacing.md),
            
            // 排序选项
            Text(
              '排序方式',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: VanHubResponsiveSpacing.sm),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<ProjectSortType>(
                    value: _sortType,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: VanHubResponsiveSpacing.md,
                        vertical: VanHubResponsiveSpacing.sm,
                      ),
                    ),
                    items: ProjectSortType.values.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Text(_getSortTypeText(type)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _sortType = value;
                        });
                      }
                    },
                  ),
                ),
                SizedBox(width: VanHubResponsiveSpacing.md),
                VanHubButtonV2(
                  text: '重置',
                  variant: VanHubButtonVariant.ghost,
                  size: VanHubButtonSize.sm,
                  onPressed: _resetFilters,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建项目网格
  Widget _buildProjectGrid() {
    final projectsAsync = ref.watch(publicProjectsProvider());

    return projectsAsync.when(
      data: (projects) {
        if (kDebugMode) {
          print('🔧 ProjectManagementPageV2: 获取到项目数据，数量: ${projects.length}');
          if (projects.isNotEmpty) {
            print('🔧 ProjectManagementPageV2: 第一个项目标题: ${projects.first.title}');
          }
        }

        final filteredProjects = _filterProjects(projects);

        if (kDebugMode) {
          print('🔧 ProjectManagementPageV2: 过滤后项目数量: ${filteredProjects.length}');
          print('🔧 ProjectManagementPageV2: 搜索查询: "$_searchQuery", 选择分类: "$_selectedCategory"');
        }

        if (filteredProjects.isEmpty) {
          if (kDebugMode) {
            print('🔧 ProjectManagementPageV2: 显示空状态');
          }
          return SliverToBoxAdapter(
            child: _buildEmptyState(),
          );
        }
        
        return SliverPadding(
          padding: EdgeInsets.all(VanHubResponsiveSpacing.lg),
          sliver: SliverMasonryGrid.count(
            crossAxisCount: VanHubResponsiveUtils.getSimpleValue<int>(
              context,
              mobile: 1,
              tablet: 2,
              desktop: 3,
            ),
            mainAxisSpacing: VanHubResponsiveSpacing.lg,
            crossAxisSpacing: VanHubResponsiveSpacing.lg,
            childCount: filteredProjects.length,
            itemBuilder: (context, index) {
              final project = filteredProjects[index];
              return _buildProjectCard(project, index);
            },
          ),
        );
      },
      loading: () => SliverToBoxAdapter(
        child: _buildLoadingState(),
      ),
      error: (error, stack) => SliverToBoxAdapter(
        child: _buildErrorState(error),
      ),
    );
  }

  /// 构建项目卡片
  Widget _buildProjectCard(dynamic project, int index) {
    // 将项目数据转换为ProjectData
    final projectData = card.ProjectData(
      id: project.id ?? '',
      name: project.title ?? '未命名项目',
      description: project.description ?? '暂无描述',
      status: _mapProjectStatus(project.status),
      priority: card.ProjectPriority.medium, // Project实体没有priority字段，使用默认值
      progress: (project.progress ?? 0.0) / 100.0,
      createdAt: project.createdAt ?? DateTime.now(),
      deadline: project.endDate,
      tags: project.tags ?? [],
      budget: project.budget ?? 0.0,
      spent: project.spentAmount ?? 0.0,
      teamSize: 1, // Project实体没有teamSize字段，使用默认值
    );

    return card.VanHubProjectCard(
      project: projectData,
      enableFlipAnimation: true,
      enableProgressAnimation: true,
      onTap: () {
        // TODO: 导航到项目详情
      },
      onEdit: () {
        // TODO: 编辑项目
      },
      onShare: () {
        // TODO: 分享项目
      },
    ).animate()
      .fadeIn(
        duration: VanHubAnimationDurations.normal,
        delay: Duration(milliseconds: index * 100),
      )
      .slideY(
        begin: 0.2,
        end: 0,
        duration: VanHubAnimationDurations.normal,
        delay: Duration(milliseconds: index * 100),
      );
  }

  /// 构建创建项目FAB
  Widget _buildCreateProjectFAB() {
    return FloatingActionButton.extended(
      heroTag: "project_fab",
      onPressed: _showCreateProjectDialog,
      backgroundColor: VanHubBrandColors.primary,
      foregroundColor: VanHubBrandColors.onPrimary,
      icon: const Icon(Icons.add),
      label: const Text('新建项目'),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(VanHubResponsiveSpacing.xxl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.folder_outlined,
              size: 80,
              color: VanHubSemanticColors.getTextColor(context, secondary: true),
            ),
            SizedBox(height: VanHubResponsiveSpacing.lg),
            Text(
              '暂无项目',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: VanHubSemanticColors.getTextColor(context, secondary: true),
              ),
            ),
            SizedBox(height: VanHubResponsiveSpacing.sm),
            Text(
              '开始您的第一个房车改装项目吧！',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: VanHubSemanticColors.getTextColor(context, secondary: true),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: VanHubResponsiveSpacing.xl),
            VanHubButtonV2.primary(
              text: '创建项目',
              leadingIcon: Icons.add,
              onPressed: _showCreateProjectDialog,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建加载状态
  Widget _buildLoadingState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(VanHubResponsiveSpacing.xxl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: VanHubBrandColors.primary,
            ),
            SizedBox(height: VanHubResponsiveSpacing.lg),
            Text(
              '加载项目中...',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: VanHubSemanticColors.getTextColor(context, secondary: true),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建错误状态
  Widget _buildErrorState(Object error) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(VanHubResponsiveSpacing.xxl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: VanHubSemanticColors.error,
            ),
            SizedBox(height: VanHubResponsiveSpacing.lg),
            Text(
              '加载失败',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: VanHubSemanticColors.error,
              ),
            ),
            SizedBox(height: VanHubResponsiveSpacing.sm),
            Text(
              error.toString(),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: VanHubSemanticColors.getTextColor(context, secondary: true),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: VanHubResponsiveSpacing.xl),
            VanHubButtonV2(
              text: '重试',
              variant: VanHubButtonVariant.outline,
              leadingIcon: Icons.refresh,
              onPressed: () {
                ref.invalidate(publicProjectsProvider());
              },
            ),
          ],
        ),
      ),
    );
  }

  // 辅助方法
  void _toggleFilters() {
    setState(() {
      _showFilters = !_showFilters;
    });
    
    if (_showFilters) {
      _filterController.forward();
    } else {
      _filterController.reverse();
    }
  }

  void _resetFilters() {
    setState(() {
      _selectedCategory = '全部';
      _sortType = ProjectSortType.newest;
      _searchQuery = '';
    });
    _searchController.clear();
  }

  void _showCreateProjectDialog() {
    showDialog(
      context: context,
      builder: (context) => const CreateProjectDialogWidget(),
    );
  }

  List<dynamic> _filterProjects(List<dynamic> projects) {
    var filtered = projects.where((project) {
      // 搜索筛选
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final title = (project.title ?? '').toLowerCase(); // 修复：使用title而不是name
        final description = (project.description ?? '').toLowerCase();
        if (!title.contains(query) && !description.contains(query)) {
          return false;
        }
      }

      // 分类筛选
      if (_selectedCategory != '全部') {
        final category = project.category ?? '';
        if (category != _selectedCategory) {
          return false;
        }
      }

      return true;
    }).toList();

    // 排序
    filtered.sort((a, b) {
      switch (_sortType) {
        case ProjectSortType.newest:
          return (b.createdAt ?? DateTime.now()).compareTo(a.createdAt ?? DateTime.now());
        case ProjectSortType.oldest:
          return (a.createdAt ?? DateTime.now()).compareTo(b.createdAt ?? DateTime.now());
        case ProjectSortType.name:
          return (a.title ?? '').compareTo(b.title ?? ''); // 修复：使用title而不是name
        case ProjectSortType.progress:
          return (b.progress ?? 0).compareTo(a.progress ?? 0);
      }
    });

    return filtered;
  }

  card.ProjectStatus _mapProjectStatus(dynamic status) {
    // 如果status是项目实体的枚举类型，映射到卡片的枚举
    if (status.toString().contains('ProjectStatus.')) {
      final statusStr = status.toString().split('.').last;
      switch (statusStr) {
        case 'planning':
          return card.ProjectStatus.planning;
        case 'inProgress':
          return card.ProjectStatus.inProgress;
        case 'completed':
          return card.ProjectStatus.completed;
        case 'paused':
          return card.ProjectStatus.paused;
        default:
          return card.ProjectStatus.planning;
      }
    }

    // 如果是字符串，按字符串映射
    if (status is String) {
      switch (status) {
        case 'planning':
          return card.ProjectStatus.planning;
        case 'in_progress':
          return card.ProjectStatus.inProgress;
        case 'paused':
          return card.ProjectStatus.paused;
        case 'completed':
          return card.ProjectStatus.completed;
        default:
          return card.ProjectStatus.planning;
      }
    }

    return card.ProjectStatus.planning;
  }

  String _getSortTypeText(ProjectSortType type) {
    switch (type) {
      case ProjectSortType.newest:
        return '最新创建';
      case ProjectSortType.oldest:
        return '最早创建';
      case ProjectSortType.name:
        return '名称排序';
      case ProjectSortType.progress:
        return '进度排序';
    }
  }
}

/// 项目排序类型
enum ProjectSortType {
  newest,   // 最新
  oldest,   // 最早
  name,     // 名称
  progress, // 进度
}
