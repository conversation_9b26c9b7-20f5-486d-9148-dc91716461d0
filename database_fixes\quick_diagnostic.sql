-- VanHub快速诊断脚本
-- 目的：快速检查关键问题

-- ========================================
-- 第一部分：基础数据检查
-- ========================================

-- 1. 检查projects表是否存在
SELECT 'projects表检查' as check_type, 
       CASE WHEN EXISTS (
           SELECT 1 FROM information_schema.tables 
           WHERE table_name = 'projects' AND table_schema = 'public'
       ) THEN '✅ 存在' ELSE '❌ 不存在' END as result;

-- 2. 统计项目数据
SELECT 'projects数据统计' as check_type,
       COUNT(*) as total_projects,
       COUNT(CASE WHEN is_public = true THEN 1 END) as public_projects,
       COUNT(CASE WHEN is_public = false THEN 1 END) as private_projects
FROM public.projects;

-- 3. 查看最近的项目（限制5条，避免大量输出）
SELECT 'recent_projects' as check_type,
       id,
       title,
       is_public,
       created_at
FROM public.projects 
ORDER BY created_at DESC 
LIMIT 5;

-- ========================================
-- 第二部分：RLS策略检查
-- ========================================

-- 4. 检查RLS是否启用
SELECT 'RLS状态检查' as check_type,
       CASE WHEN rowsecurity THEN '✅ RLS已启用' ELSE '❌ RLS未启用' END as result
FROM pg_tables 
WHERE tablename = 'projects' AND schemaname = 'public';

-- 5. 统计RLS策略数量
SELECT 'RLS策略统计' as check_type,
       COUNT(*) as policy_count,
       string_agg(policyname, ', ') as policy_names
FROM pg_policies 
WHERE tablename = 'projects' AND schemaname = 'public';

-- ========================================
-- 第三部分：权限测试
-- ========================================

-- 6. 测试公开项目查询
DO $$ 
DECLARE
    public_count INTEGER;
    error_msg TEXT;
BEGIN
    BEGIN
        SELECT COUNT(*) INTO public_count 
        FROM public.projects 
        WHERE is_public = true;
        
        RAISE NOTICE '✅ 公开项目查询成功，数量: %', public_count;
    EXCEPTION
        WHEN OTHERS THEN
            error_msg := SQLERRM;
            RAISE NOTICE '❌ 公开项目查询失败: %', error_msg;
    END;
END $$;

-- 7. 检查具体的公开项目
SELECT 'public_projects_sample' as check_type,
       COUNT(*) as count,
       MIN(created_at) as oldest,
       MAX(created_at) as newest
FROM public.projects 
WHERE is_public = true;

-- ========================================
-- 第四部分：数据完整性检查
-- ========================================

-- 8. 检查is_public字段分布
SELECT 'is_public字段分布' as check_type,
       is_public,
       COUNT(*) as count
FROM public.projects 
GROUP BY is_public
ORDER BY is_public;

-- 9. 检查user_id字段（避免UUID比较错误）
SELECT 'user_id字段检查' as check_type,
       COUNT(*) as total,
       COUNT(user_id) as non_null_count,
       COUNT(*) - COUNT(user_id) as null_count
FROM public.projects;

-- ========================================
-- 第五部分：最终诊断
-- ========================================

-- 10. 综合诊断结果
SELECT 
    '=== 快速诊断结果 ===' as summary,
    (SELECT COUNT(*) FROM public.projects) as total_projects,
    (SELECT COUNT(*) FROM public.projects WHERE is_public = true) as public_projects,
    (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'projects') as rls_policies,
    CASE 
        WHEN (SELECT COUNT(*) FROM public.projects) = 0 THEN 
            '❌ 没有项目数据'
        WHEN (SELECT COUNT(*) FROM public.projects WHERE is_public = true) = 0 THEN 
            '❌ 没有公开项目'
        WHEN (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'projects') = 0 THEN 
            '❌ 没有RLS策略'
        ELSE 
            '✅ 数据和策略都存在'
    END as diagnosis;

-- 11. 如果有数据，显示一个示例项目
DO $$ 
DECLARE
    sample_project RECORD;
BEGIN
    SELECT id, title, is_public, user_id, created_at 
    INTO sample_project
    FROM public.projects 
    WHERE is_public = true 
    ORDER BY created_at DESC 
    LIMIT 1;
    
    IF FOUND THEN
        RAISE NOTICE '✅ 示例公开项目: ID=%, 标题=%, 公开=%, 创建时间=%', 
                     sample_project.id, 
                     sample_project.title, 
                     sample_project.is_public, 
                     sample_project.created_at;
    ELSE
        RAISE NOTICE '❌ 没有找到公开项目';
    END IF;
END $$;
