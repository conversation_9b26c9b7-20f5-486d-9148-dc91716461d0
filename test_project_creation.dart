// VanHub项目创建功能测试脚本
// 用于验证数据库修复后的项目创建功能

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'lib/features/project/domain/entities/create_project_request.dart';
import 'lib/features/project/presentation/providers/project_controller.dart';

class ProjectCreationTest extends ConsumerStatefulWidget {
  const ProjectCreationTest({super.key});

  @override
  ConsumerState<ProjectCreationTest> createState() => _ProjectCreationTestState();
}

class _ProjectCreationTestState extends ConsumerState<ProjectCreationTest> {
  String _testResult = '';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('项目创建功能测试'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              '数据库修复后的项目创建功能测试',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _testBasicProjectCreation,
              child: _isLoading 
                ? const CircularProgressIndicator()
                : const Text('测试基础项目创建'),
            ),
            
            const SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _testProjectWithTags,
              child: const Text('测试带标签的项目创建'),
            ),
            
            const SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _testCompleteProjectCreation,
              child: const Text('测试完整项目创建'),
            ),
            
            const SizedBox(height: 20),
            
            const Text(
              '测试结果:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            
            const SizedBox(height: 10),
            
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _testResult.isEmpty ? '等待测试...' : _testResult,
                    style: const TextStyle(fontFamily: 'monospace'),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 测试基础项目创建（最小必填字段）
  void _testBasicProjectCreation() async {
    setState(() {
      _isLoading = true;
      _testResult = '开始测试基础项目创建...\n';
    });

    try {
      final request = CreateProjectRequest(
        title: '测试项目-基础 ${DateTime.now().millisecondsSinceEpoch}',
        description: '这是一个基础测试项目',
        vehicleType: '自行式房车',
        vehicleModel: '测试车型',
        budget: 30000.0,
        isPublic: true,
        tags: [], // 空标签数组
      );

      _appendResult('创建请求数据: ${request.toString()}\n');

      final result = await ref.read(projectControllerProvider.notifier).createProject(request);

      result.fold(
        (failure) {
          _appendResult('❌ 基础项目创建失败: ${failure.message}\n');
        },
        (project) {
          _appendResult('✅ 基础项目创建成功!\n');
          _appendResult('项目ID: ${project.id}\n');
          _appendResult('项目标题: ${project.title}\n');
          _appendResult('车辆型号: ${project.vehicleModel}\n');
          _appendResult('预算: ${project.budget}\n');
        },
      );
    } catch (e) {
      _appendResult('❌ 基础项目创建异常: $e\n');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 测试带标签的项目创建
  void _testProjectWithTags() async {
    setState(() {
      _isLoading = true;
      _testResult = '开始测试带标签的项目创建...\n';
    });

    try {
      final request = CreateProjectRequest(
        title: '测试项目-标签 ${DateTime.now().millisecondsSinceEpoch}',
        description: '这是一个带标签的测试项目',
        vehicleType: '自行式房车',
        vehicleModel: '测试车型',
        budget: 50000.0,
        isPublic: true,
        tags: ['电路系统', '水路系统', '储物系统'], // 多个标签
      );

      _appendResult('创建请求数据: ${request.toString()}\n');

      final result = await ref.read(projectControllerProvider.notifier).createProject(request);

      result.fold(
        (failure) {
          _appendResult('❌ 带标签项目创建失败: ${failure.message}\n');
        },
        (project) {
          _appendResult('✅ 带标签项目创建成功!\n');
          _appendResult('项目ID: ${project.id}\n');
          _appendResult('项目标题: ${project.title}\n');
          _appendResult('标签数量: ${project.tags?.length ?? 0}\n');
          _appendResult('标签内容: ${project.tags?.join(", ") ?? "无"}\n');
        },
      );
    } catch (e) {
      _appendResult('❌ 带标签项目创建异常: $e\n');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 测试完整项目创建（所有字段）
  void _testCompleteProjectCreation() async {
    setState(() {
      _isLoading = true;
      _testResult = '开始测试完整项目创建...\n';
    });

    try {
      final request = CreateProjectRequest(
        title: '测试项目-完整 ${DateTime.now().millisecondsSinceEpoch}',
        description: '这是一个完整的测试项目，包含所有字段信息，用于验证数据库修复后的完整功能。',
        vehicleType: '自行式房车',
        vehicleModel: '大通V80',
        vehicleBrand: '上汽大通',
        budget: 80000.0,
        isPublic: true,
        tags: ['电路系统', '水路系统', '储物系统', '床铺系统', '厨房系统'],
      );

      _appendResult('创建请求数据: ${request.toString()}\n');

      final result = await ref.read(projectControllerProvider.notifier).createProject(request);

      result.fold(
        (failure) {
          _appendResult('❌ 完整项目创建失败: ${failure.message}\n');
        },
        (project) {
          _appendResult('✅ 完整项目创建成功!\n');
          _appendResult('项目ID: ${project.id}\n');
          _appendResult('项目标题: ${project.title}\n');
          _appendResult('项目描述: ${project.description}\n');
          _appendResult('车辆品牌: ${project.vehicleBrand ?? "未设置"}\n');
          _appendResult('车辆型号: ${project.vehicleModel}\n');
          _appendResult('预算: ${project.budget}\n');
          _appendResult('是否公开: ${project.isPublic}\n');
          _appendResult('标签: ${project.tags?.join(", ") ?? "无"}\n');
          _appendResult('创建时间: ${project.createdAt}\n');
        },
      );
    } catch (e) {
      _appendResult('❌ 完整项目创建异常: $e\n');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _appendResult(String text) {
    setState(() {
      _testResult += text;
    });
  }
}
