import 'package:flutter/material.dart';
import '../../../../core/design_system/foundation/colors/colors.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';

/// 物料筛选面板组件
/// 提供高级筛选功能和现代化的用户界面
class MaterialFilterPanel extends StatefulWidget {
  final String selectedCategory;
  final ValueChanged<String> onCategoryChanged;
  final VoidCallback onReset;

  const MaterialFilterPanel({
    super.key,
    required this.selectedCategory,
    required this.onCategoryChanged,
    required this.onReset,
  });

  @override
  State<MaterialFilterPanel> createState() => _MaterialFilterPanelState();
}

class _MaterialFilterPanelState extends State<MaterialFilterPanel>
    with SingleTickerProviderStateMixin {
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  // 筛选状态
  RangeValues _priceRange = const RangeValues(0, 10000);
  String _selectedBrand = '全部';
  List<String> _selectedTags = [];
  
  // 数据
  final List<String> _categories = [
    '全部', '电路系统', '水路系统', '床铺系统', '储物系统', 
    '厨房系统', '卫浴系统', '底盘改装', '外观改装'
  ];
  
  final List<String> _brands = [
    '全部', '德力西', '正泰', '施耐德', '西门子', '飞利浦', 
    '欧普', '雷士', '松下', '美的', '海尔'
  ];
  
  final List<String> _tags = [
    '高性价比', '品质优选', '热门推荐', '新品上市', 
    '环保材料', '易安装', '免维护', '长寿命'
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: VanHubSpacing.lg),
            padding: EdgeInsets.all(VanHubSpacing.lg),
            decoration: BoxDecoration(
              color: VanHubColors.surface,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: VanHubColors.shadow.withOpacity(0.1),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
              border: Border.all(
                color: VanHubColors.outline.withOpacity(0.2),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                const SizedBox(height: VanHubSpacing.lg),
                _buildCategoryFilter(),
                const SizedBox(height: VanHubSpacing.lg),
                _buildPriceFilter(),
                const SizedBox(height: VanHubSpacing.lg),
                _buildBrandFilter(),
                const SizedBox(height: VanHubSpacing.lg),
                _buildTagsFilter(),
                const SizedBox(height: VanHubSpacing.lg),
                _buildActionButtons(),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.tune,
          color: VanHubColors.primary,
          size: 20,
        ),
        const SizedBox(width: VanHubSpacing.sm),
        Text(
          '高级筛选',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: VanHubColors.onSurface,
          ),
        ),
        const Spacer(),
        TextButton(
          onPressed: _resetFilters,
          child: Text(
            '重置',
            style: TextStyle(
              color: VanHubColors.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建分类筛选
  Widget _buildCategoryFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('材料分类'),
        const SizedBox(height: VanHubSpacing.sm),
        Wrap(
          spacing: VanHubSpacing.sm,
          runSpacing: VanHubSpacing.sm,
          children: _categories.map((category) {
            final isSelected = widget.selectedCategory == category;
            return FilterChip(
              label: Text(category),
              selected: isSelected,
              onSelected: (selected) {
                widget.onCategoryChanged(selected ? category : '全部');
              },
              backgroundColor: VanHubColors.surface,
              selectedColor: VanHubColors.primaryContainer,
              labelStyle: TextStyle(
                color: isSelected 
                    ? VanHubColors.onPrimaryContainer 
                    : VanHubColors.onSurface,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
              side: BorderSide(
                color: isSelected 
                    ? VanHubColors.primary 
                    : VanHubColors.outline,
                width: isSelected ? 2 : 1,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// 构建价格筛选
  Widget _buildPriceFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('价格区间'),
        const SizedBox(height: VanHubSpacing.sm),
        Row(
          children: [
            Text(
              '¥${_priceRange.start.round()}',
              style: TextStyle(
                fontSize: 12,
                color: VanHubColors.onSurface.withOpacity(0.7),
              ),
            ),
            Expanded(
              child: RangeSlider(
                values: _priceRange,
                min: 0,
                max: 10000,
                divisions: 100,
                labels: RangeLabels(
                  '¥${_priceRange.start.round()}',
                  '¥${_priceRange.end.round()}',
                ),
                onChanged: (values) {
                  setState(() {
                    _priceRange = values;
                  });
                },
                activeColor: VanHubColors.primary,
                inactiveColor: VanHubColors.outline.withOpacity(0.3),
              ),
            ),
            Text(
              '¥${_priceRange.end.round()}',
              style: TextStyle(
                fontSize: 12,
                color: VanHubColors.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建品牌筛选
  Widget _buildBrandFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('品牌筛选'),
        const SizedBox(height: VanHubSpacing.sm),
        Container(
          height: 40,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _brands.length,
            itemBuilder: (context, index) {
              final brand = _brands[index];
              final isSelected = _selectedBrand == brand;
              
              return Container(
                margin: EdgeInsets.only(right: VanHubSpacing.sm),
                child: FilterChip(
                  label: Text(brand),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      _selectedBrand = selected ? brand : '全部';
                    });
                  },
                  backgroundColor: VanHubColors.surface,
                  selectedColor: VanHubColors.primaryContainer,
                  labelStyle: TextStyle(
                    color: isSelected 
                        ? VanHubColors.onPrimaryContainer 
                        : VanHubColors.onSurface,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                  side: BorderSide(
                    color: isSelected 
                        ? VanHubColors.primary 
                        : VanHubColors.outline,
                    width: isSelected ? 2 : 1,
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// 构建标签筛选
  Widget _buildTagsFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('特性标签'),
        const SizedBox(height: VanHubSpacing.sm),
        Wrap(
          spacing: VanHubSpacing.sm,
          runSpacing: VanHubSpacing.sm,
          children: _tags.map((tag) {
            final isSelected = _selectedTags.contains(tag);
            return FilterChip(
              label: Text(tag),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _selectedTags.add(tag);
                  } else {
                    _selectedTags.remove(tag);
                  }
                });
              },
              backgroundColor: VanHubColors.surface,
              selectedColor: VanHubColors.primaryContainer,
              labelStyle: TextStyle(
                color: isSelected 
                    ? VanHubColors.onPrimaryContainer 
                    : VanHubColors.onSurface,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                fontSize: 12,
              ),
              side: BorderSide(
                color: isSelected 
                    ? VanHubColors.primary 
                    : VanHubColors.outline,
                width: isSelected ? 2 : 1,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _resetFilters,
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: VanHubColors.outline),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              '重置筛选',
              style: TextStyle(color: VanHubColors.onSurface),
            ),
          ),
        ),
        const SizedBox(width: VanHubSpacing.md),
        Expanded(
          child: ElevatedButton(
            onPressed: _applyFilters,
            style: ElevatedButton.styleFrom(
              backgroundColor: VanHubColors.primary,
              foregroundColor: VanHubColors.onPrimary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('应用筛选'),
          ),
        ),
      ],
    );
  }

  /// 构建章节标题
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        color: VanHubColors.onSurface,
      ),
    );
  }

  /// 重置筛选
  void _resetFilters() {
    setState(() {
      _priceRange = const RangeValues(0, 10000);
      _selectedBrand = '全部';
      _selectedTags.clear();
    });
    widget.onCategoryChanged('全部');
    widget.onReset();
  }

  /// 应用筛选
  void _applyFilters() {
    // TODO: 实现筛选逻辑
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('筛选条件已应用')),
    );
  }
}
